<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Singapore Rail Data Preview</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
          crossorigin=""/>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #map {
            height: 100vh;
            width: 100%;
        }
        
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            max-width: 300px;
        }
        
        .legend {
            margin-top: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border: 1px solid #ccc;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 2000;
        }
    </style>
</head>
<body>
    <div id="loading" class="loading">Loading Singapore Rail Data...</div>
    
    <div id="map"></div>
    
    <div class="info-panel">
        <h3>Singapore Rail Data</h3>
        <div id="feature-count">Loading...</div>
        <div class="legend">
            <h4>Legend</h4>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #E2231A; border-radius: 50%; border: 2px solid white;"></div>
                <span>Single-line stations</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #009645; border-radius: 50%; border: 3px solid #FFD700;"></div>
                <span>Interchange stations (gold border)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #2E86AB;"></div>
                <span>Rail Lines</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: rgba(162, 59, 114, 0.2); border: 1px solid #A23B72;"></div>
                <span>Areas/Infrastructure</span>
            </div>
            <div style="margin-top: 10px; font-size: 12px;">
                <strong>MRT Line Colors:</strong><br>
                <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px;">
                    <span style="background: #E2231A; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">NS</span>
                    <span style="background: #009645; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">EW</span>
                    <span style="background: #9900AA; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">NE</span>
                    <span style="background: #FA9E0D; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">CC</span>
                    <span style="background: #0099AA; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">DT</span>
                    <span style="background: #9D5B25; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">TE</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" 
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" 
            crossorigin=""></script>

    <script>
        // Initialize the map centered on Singapore
        const map = L.map('map').setView([1.3521, 103.8198], 11);

        // Base layer options for better data analysis
        const baseLayers = {
            'Light (CartoDB)': L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: 'abcd',
                maxZoom: 20
            }),
            'Dark (CartoDB)': L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: 'abcd',
                maxZoom: 20
            }),
            'Minimal (CartoDB)': L.tileLayer('https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: 'abcd',
                maxZoom: 20
            }),
            'Satellite (Esri)': L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                maxZoom: 20
            })
        };

        // Add default light layer
        baseLayers['Light (CartoDB)'].addTo(map);

        // Counters for different feature types
        let pointCount = 0;
        let lineCount = 0;
        let polygonCount = 0;

        // Function to load GeoJSON data
        async function loadGeoJSONData() {
            try {
                const response = await fetch('./data/v1/sg-rail.geojson');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                // If fetch fails (likely due to CORS), try to serve via a simple HTTP server
                throw new Error('Failed to load GeoJSON file. Please serve this HTML file via HTTP server (not file://) or use the embedded version.');
            }
        }

        // Enhanced color palette for Singapore MRT lines
        const lineColors = {
            'red': '#E2231A',      // North South Line
            'green': '#009645',    // East West Line
            'purple': '#9900AA',   // North East Line
            'yellow': '#FA9E0D',   // Circle Line
            'blue': '#0099AA',     // Downtown Line
            'brown': '#9D5B25',    // Thomson-East Coast Line
            'orange': '#FF6319',   // Future lines
            'pink': '#E899CD',     // Future lines
            'grey': '#9CA3A3',     // Default/unknown
            'gray': '#9CA3A3',     // LRT lines
            // CSS color names from the actual data
            'darkslateblue': '#483D8B',    // Downtown Line
            'darkmagenta': '#8B008B',      // North East Line
            'orangered': '#FF4500',        // North South Line
            'saddlebrown': '#8B4513',      // Thomson-East Coast Line
            'mediumseagreen': '#3CB371',   // East West Line
            'orange': '#FFA500'            // Circle Line
        };

        // Style functions for different geometry types
        function getPointStyle(feature) {
            const stationColors = feature.properties.station_colors;
            let colors = [];

            if (stationColors) {
                // Handle multi-line stations (e.g., "blue-yellow", "red-purple-yellow")
                const colorList = stationColors.split('-');
                colors = colorList.map(color => lineColors[color.toLowerCase()] || lineColors.grey);
            }

            if (colors.length === 0) {
                colors = [lineColors.grey];
            }

            // For multi-line stations, use the first color as primary
            const primaryColor = colors[0];

            return {
                radius: colors.length > 1 ? 10 : 8, // Larger for interchange stations
                fillColor: primaryColor,
                color: colors.length > 1 ? '#FFD700' : '#ffffff', // Gold border for interchanges
                weight: colors.length > 1 ? 3 : 2,
                opacity: 1,
                fillOpacity: 0.9
            };
        }

        function getLineStyle(feature) {
            const props = feature.properties;
            let color = '#2E86AB'; // Default blue

            // Use line_color property from the data
            if (props.line_color) {
                color = lineColors[props.line_color.toLowerCase()] || props.line_color;
            }

            return {
                color: color,
                weight: 4,
                opacity: 0.8,
                lineCap: 'round',
                lineJoin: 'round'
            };
        }

        function getPolygonStyle(feature) {
            return {
                fillColor: '#A23B72',
                weight: 1,
                opacity: 0.8,
                color: '#A23B72',
                fillOpacity: 0.2
            };
        }

        // Function to create popup content
        function createPopupContent(feature) {
            const props = feature.properties;
            let content = '<div style="max-width: 250px;">';

            if (props.name) {
                content += `<h4 style="margin: 0 0 10px 0;">${props.name}</h4>`;
            }

            if (props.station_codes) {
                const codes = props.station_codes.split('-');
                content += `<p><strong>Station Code(s):</strong> `;
                content += codes.map(code => `<span style="background: #f0f0f0; padding: 2px 6px; border-radius: 3px; margin: 2px;">${code}</span>`).join(' ');
                content += `</p>`;
            }

            if (props.station_colors) {
                const colors = props.station_colors.split('-');
                content += `<p><strong>Line(s):</strong> `;
                colors.forEach(color => {
                    const colorCode = lineColors[color.toLowerCase()] || '#ccc';
                    content += `<span style="background: ${colorCode}; color: white; padding: 2px 8px; border-radius: 3px; margin: 2px; display: inline-block;">${color.toUpperCase()}</span>`;
                });
                content += `</p>`;
            }

            if (props.line_color) {
                const colorCode = lineColors[props.line_color.toLowerCase()] || props.line_color;
                content += `<p><strong>Line Color:</strong> <span style="background: ${colorCode}; color: white; padding: 2px 8px; border-radius: 3px;">${props.line_color}</span></p>`;
            }

            if (props.network) {
                content += `<p><strong>Network:</strong> ${props.network}</p>`;
            }

            if (props.mode) {
                content += `<p><strong>Mode:</strong> ${props.mode}</p>`;
            }

            content += `<p><strong>Type:</strong> ${feature.geometry.type}</p>`;
            content += `<p style="font-size: 11px; color: #666;"><strong>ID:</strong> ${feature.id}</p>`;
            content += '</div>';

            return content;
        }

        // Load and display the GeoJSON data
        // Embedding data directly to avoid CORS issues with local files
        loadGeoJSONData()
            .then(data => {
                // Hide loading indicator
                document.getElementById('loading').style.display = 'none';
                
                // Separate features by geometry type for proper layering
                const lineFeatures = data.features.filter(f => f.geometry.type === 'LineString');
                const polygonFeatures = data.features.filter(f => f.geometry.type === 'Polygon');
                const pointFeatures = data.features.filter(f => f.geometry.type === 'Point');

                // Add layers in order: polygons (bottom), lines (middle), points (top)

                // 1. Add polygon layer (areas/infrastructure)
                if (polygonFeatures.length > 0) {
                    L.geoJSON({type: "FeatureCollection", features: polygonFeatures}, {
                        style: function(feature) {
                            polygonCount++;
                            return getPolygonStyle(feature);
                        },
                        onEachFeature: function(feature, layer) {
                            layer.bindPopup(createPopupContent(feature));
                        }
                    }).addTo(map);
                }

                // 2. Add line layer (rail lines)
                if (lineFeatures.length > 0) {
                    L.geoJSON({type: "FeatureCollection", features: lineFeatures}, {
                        style: function(feature) {
                            lineCount++;
                            return getLineStyle(feature);
                        },
                        onEachFeature: function(feature, layer) {
                            layer.bindPopup(createPopupContent(feature));

                            // Add hover effect for lines
                            layer.on('mouseover', function(e) {
                                layer.setStyle({
                                    weight: 6,
                                    opacity: 1
                                });
                            });

                            layer.on('mouseout', function(e) {
                                layer.setStyle(getLineStyle(feature));
                            });
                        }
                    }).addTo(map);
                }

                // 3. Add point layer (stations) - on top
                if (pointFeatures.length > 0) {
                    L.geoJSON({type: "FeatureCollection", features: pointFeatures}, {
                        pointToLayer: function (feature, latlng) {
                            pointCount++;
                            return L.circleMarker(latlng, getPointStyle(feature));
                        },
                        onEachFeature: function(feature, layer) {
                            layer.bindPopup(createPopupContent(feature));

                            // Add hover effect for stations
                            layer.on('mouseover', function(e) {
                                const currentStyle = getPointStyle(feature);
                                layer.setStyle({
                                    radius: currentStyle.radius + 2,
                                    weight: currentStyle.weight + 1
                                });
                            });

                            layer.on('mouseout', function(e) {
                                layer.setStyle(getPointStyle(feature));
                            });
                        }
                    }).addTo(map);
                }

                // Add layer control for base maps
                L.control.layers(baseLayers).addTo(map);

                // Update feature count display
                document.getElementById('feature-count').innerHTML = `
                    <strong>Features:</strong><br>
                    Points: ${pointCount}<br>
                    Lines: ${lineCount}<br>
                    Polygons: ${polygonCount}<br>
                    Total: ${pointCount + lineCount + polygonCount}
                `;
                
                // Fit map to show all features
                if (data.features && data.features.length > 0) {
                    const group = new L.featureGroup();
                    L.geoJSON(data).addTo(group);
                    map.fitBounds(group.getBounds().pad(0.1));
                }
            })
            .catch(error => {
                console.error('Error loading GeoJSON:', error);
                document.getElementById('loading').innerHTML = 'Error loading data: ' + error.message;
            });
    </script>
</body>
</html>
