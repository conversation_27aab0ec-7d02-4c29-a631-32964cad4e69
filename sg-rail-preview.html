<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Singapore Rail Data Preview</title>
    
    <!-- MapLibre GL JS CSS -->
    <link href="https://unpkg.com/maplibre-gl@4.0.2/dist/maplibre-gl.css" rel="stylesheet">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #map {
            height: 100vh;
            width: 100%;
        }
        
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 999;
            max-width: 300px;
        }

        .legend {
            margin-top: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border: 1px solid #ccc;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 2000;
        }

        .station-label, .exit-label {
            background: none !important;
            border: none !important;
        }

        .labels-control {
            position: absolute;
            top: 120px;
            right: 10px;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1001;
            font-size: 12px;
        }

        .labels-control label {
            display: block;
            margin: 5px 0;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div id="loading" class="loading">Loading Singapore Rail Data...</div>
    
    <div id="map"></div>
    
    <div class="labels-control">
        <h4>Labels</h4>
        <label>
            <input type="checkbox" id="station-labels" checked> Station Names
        </label>
        <label>
            <input type="checkbox" id="exit-labels" checked> Exit Codes
        </label>
    </div>

    <div class="info-panel">
        <h3>Singapore Rail Data</h3>
        <div id="feature-count">Loading...</div>
        <div class="legend">
            <h4>Legend</h4>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #E2231A; border-radius: 50%; border: 2px solid white;"></div>
                <span>Single-line stations</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #009645; border-radius: 50%; border: 3px solid #FFD700;"></div>
                <span>Interchange stations (gold border)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #666666; border-radius: 50%; border: 1px solid white;"></div>
                <span>Station exits (smaller, gray)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: rgba(162, 59, 114, 0.2); border: 1px solid #A23B72;"></div>
                <span>Areas/Infrastructure</span>
            </div>
            <div style="margin-top: 10px; font-size: 12px;">
                <strong>Line Colors (from data):</strong><br>
                <div style="display: flex; flex-wrap: wrap; gap: 3px; margin-top: 5px;">
                    <span style="background: #FF4500; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">NS (Red)</span>
                    <span style="background: #3CB371; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">EW (Green)</span>
                    <span style="background: #8B008B; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">NE (Purple)</span>
                    <span style="background: #FFA500; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">CC (Orange)</span>
                    <span style="background: #483D8B; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">DT (Blue)</span>
                    <span style="background: #8B4513; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">TE (Brown)</span>
                    <span style="background: #9CA3A3; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">LRT (Gray)</span>
                </div>
                <div style="margin-top: 8px; font-size: 11px; color: #666;">
                    <strong>Station Priority:</strong> Interchange stations show the color of the highest priority line (NS > EW > DT > NE > CC > TE > LRT)
                </div>
            </div>
        </div>
    </div>

    <!-- MapLibre GL JS JavaScript -->
    <script src="https://unpkg.com/maplibre-gl@4.0.2/dist/maplibre-gl.js"></script>

    <script>
        // Initialize MapLibre GL JS map centered on Singapore
        const map = new maplibregl.Map({
            container: 'map',
            style: {
                version: 8,
                sources: {
                    'osm': {
                        type: 'raster',
                        tiles: [
                            'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png'
                        ],
                        tileSize: 256,
                        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>'
                    }
                },
                layers: [
                    {
                        id: 'osm',
                        type: 'raster',
                        source: 'osm'
                    }
                ]
            },
            center: [103.8198, 1.3521], // Singapore coordinates [lng, lat]
            zoom: 10
        });

        // Counters for different feature types
        let stationCount = 0;
        let exitCount = 0;
        let lineCount = 0;
        let polygonCount = 0;

        // Store label visibility state
        let showStationLabels = true;
        let showExitLabels = true;

        // Function to load GeoJSON data
        async function loadGeoJSONData() {
            try {
                const response = await fetch('./data/v1/sg-rail.geojson');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                // If fetch fails (likely due to CORS), try to serve via a simple HTTP server
                throw new Error('Failed to load GeoJSON file. Please serve this HTML file via HTTP server (not file://) or use the embedded version.');
            }
        }

        // Enhanced color palette for Singapore MRT lines
        const lineColors = {
            'red': '#E2231A',      // North South Line
            'green': '#009645',    // East West Line
            'purple': '#9900AA',   // North East Line
            'yellow': '#FA9E0D',   // Circle Line
            'blue': '#0099AA',     // Downtown Line
            'brown': '#9D5B25',    // Thomson-East Coast Line
            'orange': '#FF6319',   // Future lines
            'pink': '#E899CD',     // Future lines
            'grey': '#9CA3A3',     // Default/unknown
            'gray': '#9CA3A3',     // LRT lines
            // CSS color names from the actual data
            'darkslateblue': '#483D8B',    // Downtown Line
            'darkmagenta': '#8B008B',      // North East Line
            'orangered': '#FF4500',        // North South Line
            'saddlebrown': '#8B4513',      // Thomson-East Coast Line
            'mediumseagreen': '#3CB371',   // East West Line
            'orange': '#FFA500'            // Circle Line
        };

        // Function to get station color based on line colors
        function getStationColor(feature) {
            const stationColors = feature.properties.station_colors;
            if (!stationColors) return lineColors.grey;

            const colorList = stationColors.split('-').filter(color => color.trim() !== '');
            const uniqueColors = [...new Set(colorList)];
            const colors = uniqueColors.map(color => lineColors[color.toLowerCase()] || lineColors.grey);

            if (colors.length === 0) return lineColors.grey;

            // For multi-line stations, use priority order
            const priorityOrder = [
                lineColors.red, lineColors.green, lineColors.blue,
                lineColors.purple, lineColors.yellow, lineColors.brown
            ];

            for (const priorityColor of priorityOrder) {
                if (colors.includes(priorityColor)) {
                    return priorityColor;
                }
            }

            return colors[0];
        }

        // Function to determine if station is an interchange
        function isInterchangeStation(feature) {
            const stationColors = feature.properties.station_colors;
            if (!stationColors) return false;

            const colorList = stationColors.split('-').filter(color => color.trim() !== '');
            const uniqueColors = [...new Set(colorList)];
            return uniqueColors.length > 1;
        }

        // Wait for map to load, then add data
        map.on('load', async () => {
            try {
                // Show loading indicator
                document.getElementById('loading').style.display = 'block';

                // Load GeoJSON data
                const data = await loadGeoJSONData();

                // Separate features by type
                const stations = data.features.filter(f =>
                    f.geometry.type === 'Point' && f.properties.stop_type === 'station'
                );
                const exits = data.features.filter(f =>
                    f.geometry.type === 'Point' && f.properties.stop_type === 'entrance'
                );
                const lines = data.features.filter(f =>
                    f.geometry.type === 'LineString' || f.geometry.type === 'MultiLineString'
                );
                const polygons = data.features.filter(f =>
                    f.geometry.type === 'Polygon'
                );

                // Update counters
                stationCount = stations.length;
                exitCount = exits.length;
                lineCount = lines.length;
                polygonCount = polygons.length;

                // Add data sources
                map.addSource('stations', {
                    type: 'geojson',
                    data: { type: 'FeatureCollection', features: stations }
                });

                map.addSource('exits', {
                    type: 'geojson',
                    data: { type: 'FeatureCollection', features: exits }
                });

                map.addSource('lines', {
                    type: 'geojson',
                    data: { type: 'FeatureCollection', features: lines }
                });

                map.addSource('polygons', {
                    type: 'geojson',
                    data: { type: 'FeatureCollection', features: polygons }
                });

                // Add polygon layer (infrastructure areas)
                map.addLayer({
                    id: 'polygons',
                    type: 'fill',
                    source: 'polygons',
                    paint: {
                        'fill-color': '#A23B72',
                        'fill-opacity': 0.2
                    }
                });

                map.addLayer({
                    id: 'polygons-outline',
                    type: 'line',
                    source: 'polygons',
                    paint: {
                        'line-color': '#A23B72',
                        'line-width': 1,
                        'line-opacity': 0.8
                    }
                });

                // Add line layer (rail lines)
                map.addLayer({
                    id: 'lines',
                    type: 'line',
                    source: 'lines',
                    paint: {
                        'line-color': [
                            'case',
                            ['has', 'line_color'],
                            ['get', 'line_color'],
                            '#2E86AB'
                        ],
                        'line-width': 4,
                        'line-opacity': 0.8
                    },
                    layout: {
                        'line-cap': 'round',
                        'line-join': 'round'
                    }
                });

                // Add exit layer (smaller gray circles)
                map.addLayer({
                    id: 'exits',
                    type: 'circle',
                    source: 'exits',
                    paint: {
                        'circle-radius': 4,
                        'circle-color': '#666666',
                        'circle-stroke-color': '#ffffff',
                        'circle-stroke-width': 1,
                        'circle-opacity': 0.8
                    }
                });

                // Add station layer (larger colored circles on top)
                map.addLayer({
                    id: 'stations',
                    type: 'circle',
                    source: 'stations',
                    paint: {
                        'circle-radius': [
                            'case',
                            ['>', ['length', ['split', '-', ['get', 'station_colors']]], 1],
                            10, // Interchange stations
                            8   // Regular stations
                        ],
                        'circle-color': [
                            'case',
                            ['has', 'station_colors'],
                            [
                                'case',
                                ['in', 'red', ['get', 'station_colors']], '#E2231A',
                                ['in', 'green', ['get', 'station_colors']], '#009645',
                                ['in', 'blue', ['get', 'station_colors']], '#0099AA',
                                ['in', 'purple', ['get', 'station_colors']], '#9900AA',
                                ['in', 'yellow', ['get', 'station_colors']], '#FA9E0D',
                                ['in', 'brown', ['get', 'station_colors']], '#9D5B25',
                                '#9CA3A3'
                            ],
                            '#9CA3A3'
                        ],
                        'circle-stroke-color': [
                            'case',
                            ['>', ['length', ['split', '-', ['get', 'station_colors']]], 1],
                            '#FFD700', // Gold border for interchanges
                            '#ffffff'  // White border for regular stations
                        ],
                        'circle-stroke-width': [
                            'case',
                            ['>', ['length', ['split', '-', ['get', 'station_colors']]], 1],
                            3, // Thicker border for interchanges
                            2  // Regular border
                        ],
                        'circle-opacity': 1
                    }
                });

                // Add labels for stations
                map.addSource('station-labels', {
                    type: 'geojson',
                    data: { type: 'FeatureCollection', features: stations }
                });

                map.addLayer({
                    id: 'station-labels',
                    type: 'symbol',
                    source: 'station-labels',
                    layout: {
                        'text-field': ['get', 'name'],
                        'text-font': ['Open Sans Bold', 'Arial Unicode MS Bold'],
                        'text-size': 11,
                        'text-offset': [0, -1.5],
                        'text-anchor': 'center'
                    },
                    paint: {
                        'text-color': '#333333',
                        'text-halo-color': '#ffffff',
                        'text-halo-width': 2
                    }
                });

                // Add labels for exits
                map.addSource('exit-labels', {
                    type: 'geojson',
                    data: { type: 'FeatureCollection', features: exits }
                });

                map.addLayer({
                    id: 'exit-labels',
                    type: 'symbol',
                    source: 'exit-labels',
                    layout: {
                        'text-field': ['get', 'name'],
                        'text-font': ['Open Sans Regular', 'Arial Unicode MS Regular'],
                        'text-size': 10,
                        'text-offset': [0, -0.8],
                        'text-anchor': 'center'
                    },
                    paint: {
                        'text-color': '#666666',
                        'text-halo-color': '#ffffff',
                        'text-halo-width': 1
                    }
                });

        // Function to create popup content
        function createPopupContent(feature) {
            const props = feature.properties;
            let content = '<div style="max-width: 250px;">';

            if (props.name) {
                content += `<h4 style="margin: 0 0 10px 0;">${props.name}</h4>`;
            }

            if (props.station_codes) {
                const codes = props.station_codes.split('-');
                content += `<p><strong>Station Code(s):</strong> `;
                content += codes.map(code => `<span style="background: #f0f0f0; padding: 2px 6px; border-radius: 3px; margin: 2px;">${code}</span>`).join(' ');
                content += `</p>`;
            }

            if (props.station_colors) {
                const colors = props.station_colors.split('-');
                content += `<p><strong>Line(s):</strong> `;
                colors.forEach(color => {
                    const colorCode = lineColors[color.toLowerCase()] || '#ccc';
                    content += `<span style="background: ${colorCode}; color: white; padding: 2px 8px; border-radius: 3px; margin: 2px; display: inline-block;">${color.toUpperCase()}</span>`;
                });
                content += `</p>`;
            }

            if (props.line_color) {
                const colorCode = lineColors[props.line_color.toLowerCase()] || props.line_color;
                content += `<p><strong>Line Color:</strong> <span style="background: ${colorCode}; color: white; padding: 2px 8px; border-radius: 3px;">${props.line_color}</span></p>`;
            }

            if (props.network) {
                content += `<p><strong>Network:</strong> ${props.network}</p>`;
            }

            if (props.mode) {
                content += `<p><strong>Mode:</strong> ${props.mode}</p>`;
            }

            content += `<p><strong>Type:</strong> ${feature.geometry.type}</p>`;
            content += `<p style="font-size: 11px; color: #666;"><strong>ID:</strong> ${feature.id}</p>`;
            content += '</div>';

            return content;
        }

                // Add click handlers for popups
                map.on('click', 'stations', (e) => {
                    const feature = e.features[0];
                    new maplibregl.Popup()
                        .setLngLat(e.lngLat)
                        .setHTML(createPopupContent(feature))
                        .addTo(map);
                });

                map.on('click', 'exits', (e) => {
                    const feature = e.features[0];
                    new maplibregl.Popup()
                        .setLngLat(e.lngLat)
                        .setHTML(createPopupContent(feature))
                        .addTo(map);
                });

                map.on('click', 'lines', (e) => {
                    const feature = e.features[0];
                    new maplibregl.Popup()
                        .setLngLat(e.lngLat)
                        .setHTML(createPopupContent(feature))
                        .addTo(map);
                });

                map.on('click', 'polygons', (e) => {
                    const feature = e.features[0];
                    new maplibregl.Popup()
                        .setLngLat(e.lngLat)
                        .setHTML(createPopupContent(feature))
                        .addTo(map);
                });

                // Change cursor on hover
                map.on('mouseenter', 'stations', () => {
                    map.getCanvas().style.cursor = 'pointer';
                });
                map.on('mouseleave', 'stations', () => {
                    map.getCanvas().style.cursor = '';
                });

                map.on('mouseenter', 'exits', () => {
                    map.getCanvas().style.cursor = 'pointer';
                });
                map.on('mouseleave', 'exits', () => {
                    map.getCanvas().style.cursor = '';
                });

                // Update feature count display
                document.getElementById('feature-count').innerHTML = `
                    <strong>Features:</strong><br>
                    Stations: ${stationCount}<br>
                    Exits: ${exitCount}<br>
                    Lines: ${lineCount}<br>
                    Polygons: ${polygonCount}<br>
                    Total: ${stationCount + exitCount + lineCount + polygonCount}
                `;

                // Hide loading indicator
                document.getElementById('loading').style.display = 'none';

            } catch (error) {
                console.error('Error loading GeoJSON:', error);
                document.getElementById('loading').innerHTML = 'Error loading data: ' + error.message;
            }
        });
            .then(data => {
                // Hide loading indicator
                document.getElementById('loading').style.display = 'none';
                
                // Separate features by geometry type and stop_type for proper layering
                const lineFeatures = data.features.filter(f => f.geometry.type === 'LineString' || f.geometry.type === 'MultiLineString');
                const polygonFeatures = data.features.filter(f => f.geometry.type === 'Polygon');
                const exitFeatures = data.features.filter(f => f.geometry.type === 'Point' && f.properties.stop_type === 'entrance');
                const stationFeatures = data.features.filter(f => f.geometry.type === 'Point' && f.properties.stop_type === 'station');

                // Add layers in order: polygons (bottom), lines, exits, stations (top)

                // 1. Add polygon layer (areas/infrastructure)
                if (polygonFeatures.length > 0) {
                    L.geoJSON({type: "FeatureCollection", features: polygonFeatures}, {
                        style: function(feature) {
                            polygonCount++;
                            return getPolygonStyle(feature);
                        },
                        onEachFeature: function(feature, layer) {
                            layer.bindPopup(createPopupContent(feature));
                        }
                    }).addTo(map);
                }

                // 2. Add line layer (rail lines)
                if (lineFeatures.length > 0) {
                    L.geoJSON({type: "FeatureCollection", features: lineFeatures}, {
                        style: function(feature) {
                            lineCount++;
                            return getLineStyle(feature);
                        },
                        onEachFeature: function(feature, layer) {
                            layer.bindPopup(createPopupContent(feature));

                            // Add hover effect for lines
                            layer.on('mouseover', function(e) {
                                if (feature.geometry.type === 'LineString' || feature.geometry.type === 'MultiLineString') {
                                    layer.setStyle({
                                        weight: 6,
                                        opacity: 1
                                    });
                                }
                            });

                            layer.on('mouseout', function(e) {
                                if (feature.geometry.type === 'LineString' || feature.geometry.type === 'MultiLineString') {
                                    layer.setStyle(getLineStyle(feature));
                                }
                            });
                        }
                    }).addTo(map);
                }

                // 3. Add exit layer (station exits) - below stations
                if (exitFeatures.length > 0) {
                    L.geoJSON({type: "FeatureCollection", features: exitFeatures}, {
                        pointToLayer: function (feature, latlng) {
                            exitCount++;
                            const marker = L.circleMarker(latlng, getExitStyle(feature));

                            // Add text label for exit
                            if (feature.properties.name) {
                                const label = L.divIcon({
                                    className: 'exit-label',
                                    html: `<div style="font-size: 10px; color: #666; font-weight: bold; text-shadow: 1px 1px 1px white; pointer-events: none;">${feature.properties.name}</div>`,
                                    iconSize: [20, 10],
                                    iconAnchor: [10, -5]
                                });
                                L.marker(latlng, {icon: label}).addTo(exitLabels);
                            }

                            return marker;
                        },
                        onEachFeature: function(feature, layer) {
                            layer.bindPopup(createPopupContent(feature));

                            // Add hover effect for exits
                            layer.on('mouseover', function(e) {
                                layer.setStyle({
                                    radius: 6,
                                    weight: 2
                                });
                            });

                            layer.on('mouseout', function(e) {
                                layer.setStyle(getExitStyle(feature));
                            });
                        }
                    }).addTo(map);
                }

                // 4. Add station layer (stations) - on top
                if (stationFeatures.length > 0) {
                    L.geoJSON({type: "FeatureCollection", features: stationFeatures}, {
                        pointToLayer: function (feature, latlng) {
                            stationCount++;
                            const marker = L.circleMarker(latlng, getPointStyle(feature));

                            // Add text label for station
                            if (feature.properties.name) {
                                const isInterchange = feature.properties.station_colors && feature.properties.station_colors.includes('-');
                                const label = L.divIcon({
                                    className: 'station-label',
                                    html: `<div style="font-size: 11px; color: #333; font-weight: bold; text-shadow: 1px 1px 2px white; pointer-events: none; text-align: center;">${feature.properties.name}</div>`,
                                    iconSize: [100, 12],
                                    iconAnchor: [50, -15]
                                });
                                L.marker(latlng, {icon: label}).addTo(stationLabels);
                            }

                            return marker;
                        },
                        onEachFeature: function(feature, layer) {
                            layer.bindPopup(createPopupContent(feature));

                            // Add hover effect for stations
                            layer.on('mouseover', function(e) {
                                const currentStyle = getPointStyle(feature);
                                layer.setStyle({
                                    radius: currentStyle.radius + 2,
                                    weight: currentStyle.weight + 1
                                });
                            });

                            layer.on('mouseout', function(e) {
                                layer.setStyle(getPointStyle(feature));
                            });
                        }
                    }).addTo(map);
                }

                // Add layer control for base maps
                L.control.layers(baseLayers).addTo(map);

                // Update feature count display
                document.getElementById('feature-count').innerHTML = `
                    <strong>Features:</strong><br>
                    Stations: ${stationCount}<br>
                    Exits: ${exitCount}<br>
                    Lines: ${lineCount}<br>
                    Polygons: ${polygonCount}<br>
                    Total: ${stationCount + exitCount + lineCount + polygonCount}
                `;
                
                // Fit map to show all features
                if (data.features && data.features.length > 0) {
                    const group = new L.featureGroup();
                    L.geoJSON(data).addTo(group);
                    map.fitBounds(group.getBounds().pad(0.1));
                }
            })
            .catch(error => {
                console.error('Error loading GeoJSON:', error);
                document.getElementById('loading').innerHTML = 'Error loading data: ' + error.message;
            });

        // Add label toggle functionality
        document.getElementById('station-labels').addEventListener('change', function(e) {
            if (e.target.checked) {
                map.addLayer(stationLabels);
            } else {
                map.removeLayer(stationLabels);
            }
        });

        document.getElementById('exit-labels').addEventListener('change', function(e) {
            if (e.target.checked) {
                map.addLayer(exitLabels);
            } else {
                map.removeLayer(exitLabels);
            }
        });
    </script>
</body>
</html>
