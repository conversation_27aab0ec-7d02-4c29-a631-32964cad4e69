<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Singapore Rail Data Preview</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
          crossorigin=""/>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #map {
            height: 100vh;
            width: 100%;
        }
        
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            max-width: 300px;
        }
        
        .legend {
            margin-top: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border: 1px solid #ccc;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 2000;
        }
    </style>
</head>
<body>
    <div id="loading" class="loading">Loading Singapore Rail Data...</div>
    
    <div id="map"></div>
    
    <div class="info-panel">
        <h3>Singapore Rail Data</h3>
        <div id="feature-count">Loading...</div>
        <div class="legend">
            <h4>Legend</h4>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #E2231A; border-radius: 50%; border: 2px solid white;"></div>
                <span>Stations (color-coded by line)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #2E86AB;"></div>
                <span>Rail Lines</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: rgba(162, 59, 114, 0.2); border: 1px solid #A23B72;"></div>
                <span>Areas/Infrastructure</span>
            </div>
            <div style="margin-top: 10px; font-size: 12px;">
                <strong>MRT Line Colors:</strong><br>
                <div style="display: flex; flex-wrap: wrap; gap: 5px; margin-top: 5px;">
                    <span style="background: #E2231A; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">NS</span>
                    <span style="background: #009645; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">EW</span>
                    <span style="background: #9900AA; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">NE</span>
                    <span style="background: #FA9E0D; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">CC</span>
                    <span style="background: #0099AA; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">DT</span>
                    <span style="background: #9D5B25; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">TE</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" 
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" 
            crossorigin=""></script>

    <script>
        // Initialize the map centered on Singapore
        const map = L.map('map').setView([1.3521, 103.8198], 11);

        // Base layer options for better data analysis
        const baseLayers = {
            'Light (CartoDB)': L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: 'abcd',
                maxZoom: 20
            }),
            'Dark (CartoDB)': L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: 'abcd',
                maxZoom: 20
            }),
            'Minimal (CartoDB)': L.tileLayer('https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: 'abcd',
                maxZoom: 20
            }),
            'Satellite (Esri)': L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                maxZoom: 20
            })
        };

        // Add default light layer
        baseLayers['Light (CartoDB)'].addTo(map);

        // Counters for different feature types
        let pointCount = 0;
        let lineCount = 0;
        let polygonCount = 0;

        // Function to load GeoJSON data
        async function loadGeoJSONData() {
            try {
                const response = await fetch('./data/v1/sg-rail.geojson');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                // If fetch fails (likely due to CORS), try to serve via a simple HTTP server
                throw new Error('Failed to load GeoJSON file. Please serve this HTML file via HTTP server (not file://) or use the embedded version.');
            }
        }

        // Enhanced color palette for Singapore MRT lines
        const lineColors = {
            'red': '#E2231A',      // North South Line
            'green': '#009645',    // East West Line
            'purple': '#9900AA',   // North East Line
            'yellow': '#FA9E0D',   // Circle Line
            'blue': '#0099AA',     // Downtown Line
            'brown': '#9D5B25',    // Thomson-East Coast Line
            'orange': '#FF6319',   // Future lines
            'pink': '#E899CD',     // Future lines
            'grey': '#9CA3A3'      // Default/unknown
        };

        // Style functions for different geometry types
        function getPointStyle(feature) {
            const stationColor = feature.properties.station_colors;
            let color = lineColors.grey;

            if (stationColor && lineColors[stationColor.toLowerCase()]) {
                color = lineColors[stationColor.toLowerCase()];
            }

            return {
                radius: 8,
                fillColor: color,
                color: '#ffffff',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.9
            };
        }

        function getLineStyle(feature) {
            // Try to determine line color from properties
            const props = feature.properties;
            let color = '#2E86AB'; // Default blue

            if (props.colour) {
                color = props.colour;
            } else if (props.station_colors && lineColors[props.station_colors.toLowerCase()]) {
                color = lineColors[props.station_colors.toLowerCase()];
            }

            return {
                color: color,
                weight: 4,
                opacity: 0.9,
                lineCap: 'round',
                lineJoin: 'round'
            };
        }

        function getPolygonStyle(feature) {
            return {
                fillColor: '#A23B72',
                weight: 1,
                opacity: 0.8,
                color: '#A23B72',
                fillOpacity: 0.2
            };
        }

        // Function to create popup content
        function createPopupContent(feature) {
            const props = feature.properties;
            let content = '<div style="max-width: 200px;">';
            
            if (props.name) {
                content += `<h4>${props.name}</h4>`;
            }
            
            if (props.station_codes) {
                content += `<p><strong>Station Code:</strong> ${props.station_codes}</p>`;
            }
            
            if (props.station_colors) {
                content += `<p><strong>Line Color:</strong> ${props.station_colors}</p>`;
            }
            
            if (props.network) {
                content += `<p><strong>Network:</strong> ${props.network}</p>`;
            }
            
            if (props.mode) {
                content += `<p><strong>Mode:</strong> ${props.mode}</p>`;
            }
            
            content += `<p><strong>Geometry:</strong> ${feature.geometry.type}</p>`;
            content += `<p><strong>ID:</strong> ${feature.id}</p>`;
            content += '</div>';
            
            return content;
        }

        // Load and display the GeoJSON data
        // Embedding data directly to avoid CORS issues with local files
        loadGeoJSONData()
            .then(data => {
                // Hide loading indicator
                document.getElementById('loading').style.display = 'none';
                
                // Add GeoJSON layer with custom styling
                L.geoJSON(data, {
                    pointToLayer: function (feature, latlng) {
                        pointCount++;
                        return L.circleMarker(latlng, getPointStyle(feature));
                    },
                    style: function(feature) {
                        if (feature.geometry.type === 'LineString') {
                            lineCount++;
                            return getLineStyle(feature);
                        } else if (feature.geometry.type === 'Polygon') {
                            polygonCount++;
                            return getPolygonStyle(feature);
                        }
                    },
                    onEachFeature: function(feature, layer) {
                        // Add popup with feature information
                        layer.bindPopup(createPopupContent(feature));
                        
                        // Add hover effect
                        layer.on('mouseover', function(e) {
                            if (feature.geometry.type !== 'Point') {
                                layer.setStyle({
                                    weight: 5,
                                    opacity: 1
                                });
                            }
                        });
                        
                        layer.on('mouseout', function(e) {
                            if (feature.geometry.type === 'LineString') {
                                layer.setStyle(getLineStyle(feature));
                            } else if (feature.geometry.type === 'Polygon') {
                                layer.setStyle(getPolygonStyle(feature));
                            }
                        });
                    }
                }).addTo(map);

                // Add layer control for base maps
                L.control.layers(baseLayers).addTo(map);

                // Update feature count display
                document.getElementById('feature-count').innerHTML = `
                    <strong>Features:</strong><br>
                    Points: ${pointCount}<br>
                    Lines: ${lineCount}<br>
                    Polygons: ${polygonCount}<br>
                    Total: ${pointCount + lineCount + polygonCount}
                `;
                
                // Fit map to show all features
                if (data.features && data.features.length > 0) {
                    const group = new L.featureGroup();
                    L.geoJSON(data).addTo(group);
                    map.fitBounds(group.getBounds().pad(0.1));
                }
            })
            .catch(error => {
                console.error('Error loading GeoJSON:', error);
                document.getElementById('loading').innerHTML = 'Error loading data: ' + error.message;
            });
    </script>
</body>
</html>
