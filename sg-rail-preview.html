<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Singapore Rail Data Preview</title>
    
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
        integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
        crossorigin=""/>
    
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: Arial, sans-serif;
    }
    #map {
      height: 100vh;
      width: 100%;
    }
    .info-panel {
      position: absolute;
      top: 10px;
      right: 10px;
      background: white;
      padding: 15px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      z-index: 999;
      max-width: 300px;
    }
    .loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      z-index: 2000;
    }
    .station-label, .exit-label {
      background: none !important;
      border: none !important;
    }
  </style>
</head>
<body>
  <div id="loading" class="loading">Loading Singapore Rail Data...</div>
  <div id="map"></div>
  <div class="info-panel">
    <h3>Singapore Rail Data</h3>
    <div id="feature-count">Loading...</div>
  </div>
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
          integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
          crossorigin=""></script>

  <script>
    const map = L.map('map').setView([1.3521, 103.8198], 11);
    L.tileLayer('https://tiles.openfreemap.org/styles/liberty/{z}/{x}/{y}.png', {
      attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      maxZoom: 19
    }).addTo(map);

    let stationCount = 0;
    let exitCount = 0;
    let lineCount = 0;
    let polygonCount = 0;
    let stationLabels = L.layerGroup().addTo(map);
    let exitLabels = L.layerGroup().addTo(map);

    async function loadGeoJSONData() {
      try {
        const response = await fetch('./data/v1/sg-rail.geojson');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
      } catch (error) {
        throw new Error('Failed to load GeoJSON file. Please serve this HTML file via HTTP server (not file://) or use the embedded version.');
      }
    }

    const lineColors = {
      'red': '#E2231A',
      'green': '#009645',
      'purple': '#9900AA',
      'yellow': '#FA9E0D',
      'blue': '#0099AA',
      'brown': '#9D5B25',
      'orange': '#FF6319',
      'pink': '#E899CD',
      'grey': '#9CA3A3',
      'gray': '#9CA3A3',
      'darkslateblue': '#0099AA',
      'darkmagenta': '#8B008B',
      'orangered': '#FF4500',
      'saddlebrown': '#8B4513',
      'mediumseagreen': '#3CB371'
    };

    function getStationColors(stationColors) {
      if (!stationColors) return [];
      const colorList = stationColors.split('-').filter(color => color.trim() !== '');
      return [...new Set(colorList)].map(color => lineColors[color.toLowerCase()] || lineColors.grey);
    }

    function getStationColor(feature) {
      const colors = getStationColors(feature.properties.station_colors);
      if (colors.length === 0) return lineColors.grey;
      const priorityOrder = [lineColors.red, lineColors.green, lineColors.blue, lineColors.purple, lineColors.yellow, lineColors.brown];
      for (const priorityColor of priorityOrder) {
        if (colors.includes(priorityColor)) return priorityColor;
      }
      return colors[0];
    }

    function isInterchangeStation(feature) {
      return getStationColors(feature.properties.station_colors).length > 1;
    }

    function getPointStyle(feature) {
      const isInterchange = isInterchangeStation(feature);
      const primaryColor = getStationColor(feature);
      return {
        radius: isInterchange ? 10 : 8,
        fillColor: primaryColor,
        color: isInterchange ? '#000000' : '#ffffff',
        weight: isInterchange ? 3 : 2,
        opacity: 1,
        fillOpacity: 0.9
      };
    }

    function getLineStyle(feature) {
      const props = feature.properties;
      let color = '#2E86AB';
      if (props.line_color) {
        color = lineColors[props.line_color.toLowerCase()] || props.line_color;
      }
      return {
        color: color,
        weight: 4,
        opacity: 0.8,
        lineCap: 'round',
        lineJoin: 'round'
      };
    }

    function getPolygonStyle(feature) {
      return {
        fillColor: '#A23B72',
        weight: 1,
        opacity: 0.8,
        color: '#A23B72',
        fillOpacity: 0.2
      };
    }

    function getExitStyle(feature) {
      return {
        radius: 4,
        fillColor: '#666666',
        color: '#ffffff',
        weight: 1,
        opacity: 0.8,
        fillOpacity: 0.7
      };
    }

    function createPopupContent(feature) {
      const props = feature.properties;
      let content = '<div style="max-width: 300px;">';
      if (props.name) {
        content += `<h4 style="margin: 0 0 10px 0; color: #333;">${props.name}</h4>`;
      }
      if (props.stop_type) {
        const typeLabel = props.stop_type === 'station' ? 'Station' : 'Exit/Entrance';
        content += `<p style="margin: 5px 0;"><strong>Type:</strong> ${typeLabel}</p>`;
      }
      if (props.station_colors) {
        content += `<p style="margin: 5px 0;"><strong>Lines:</strong> ${props.station_colors}</p>`;
      }
      if (props.line_color) {
        content += `<p style="margin: 5px 0;"><strong>Line Color:</strong> ${props.line_color}</p>`;
      }
      content += '<div style="margin-top: 10px; font-size: 11px; color: #666;">';
      content += '<strong>All Properties:</strong><br>';
      for (const [key, value] of Object.entries(props)) {
        if (!['name', 'stop_type', 'station_colors', 'line_color'].includes(key)) {
          content += `${key}: ${value}<br>`;
        }
      }
      content += '</div></div>';
      return content;
    }

    loadGeoJSONData()
      .then(data => {
        document.getElementById('loading').style.display = 'none';
        const lineFeatures = data.features.filter(f => f.geometry.type === 'LineString' || f.geometry.type === 'MultiLineString');
        const polygonFeatures = data.features.filter(f => f.geometry.type === 'Polygon');
        const exitFeatures = data.features.filter(f => f.geometry.type === 'Point' && f.properties.stop_type === 'entrance');
        const stationFeatures = data.features.filter(f => f.geometry.type === 'Point' && f.properties.stop_type === 'station');

        if (polygonFeatures.length > 0) {
          L.geoJSON({type: "FeatureCollection", features: polygonFeatures}, {
            style: function(feature) {
              polygonCount++;
              return getPolygonStyle(feature);
            },
            onEachFeature: function(feature, layer) {
              layer.bindPopup(createPopupContent(feature));
            }
          }).addTo(map);
        }

        if (lineFeatures.length > 0) {
          L.geoJSON({type: "FeatureCollection", features: lineFeatures}, {
            style: function(feature) {
              lineCount++;
              return getLineStyle(feature);
            },
            onEachFeature: function(feature, layer) {
              layer.bindPopup(createPopupContent(feature));
              layer.on('mouseover', function(e) {
                layer.setStyle({weight: 6, opacity: 1});
              });
              layer.on('mouseout', function(e) {
                layer.setStyle(getLineStyle(feature));
              });
            }
          }).addTo(map);
        }

        if (exitFeatures.length > 0) {
          L.geoJSON({type: "FeatureCollection", features: exitFeatures}, {
            pointToLayer: function (feature, latlng) {
              exitCount++;
              const marker = L.circleMarker(latlng, getExitStyle(feature));
              if (feature.properties.name) {
                const label = L.divIcon({
                  className: 'exit-label',
                  html: `<div style="font-size: 10px; color: #666; font-weight: bold; text-shadow: 1px 1px 1px white; pointer-events: none;">${feature.properties.name}</div>`,
                  iconSize: [20, 10],
                  iconAnchor: [10, -5]
                });
                L.marker(latlng, {icon: label}).addTo(exitLabels);
              }
              return marker;
            },
            onEachFeature: function(feature, layer) {
              layer.bindPopup(createPopupContent(feature));
              layer.on('mouseover', function(e) {
                layer.setStyle({radius: 6, weight: 2});
              });
              layer.on('mouseout', function(e) {
                layer.setStyle(getExitStyle(feature));
              });
            }
          }).addTo(map);
        }

        if (stationFeatures.length > 0) {
          L.geoJSON({type: "FeatureCollection", features: stationFeatures}, {
            pointToLayer: function (feature, latlng) {
              stationCount++;
              const marker = L.circleMarker(latlng, getPointStyle(feature));
              if (feature.properties.name) {
                const label = L.divIcon({
                  className: 'station-label',
                  html: `<div style="font-size: 11px; color: #333; font-weight: bold; text-shadow: 1px 1px 2px white; pointer-events: none; text-align: center;">${feature.properties.name}</div>`,
                  iconSize: [100, 12],
                  iconAnchor: [50, -15]
                });
                L.marker(latlng, {icon: label}).addTo(stationLabels);
              }
              return marker;
            },
            onEachFeature: function(feature, layer) {
              layer.bindPopup(createPopupContent(feature));
              layer.on('mouseover', function(e) {
                const currentStyle = getPointStyle(feature);
                layer.setStyle({
                  radius: currentStyle.radius + 2,
                  weight: currentStyle.weight + 1
                });
              });
              layer.on('mouseout', function(e) {
                layer.setStyle(getPointStyle(feature));
              });
            }
          }).addTo(map);
        }

        document.getElementById('feature-count').innerHTML = `
          <strong>Features:</strong><br>
          Stations: ${stationCount}<br>
          Exits: ${exitCount}<br>
          Lines: ${lineCount}<br>
          Polygons: ${polygonCount}<br>
          Total: ${stationCount + exitCount + lineCount + polygonCount}
        `;
        if (data.features && data.features.length > 0) {
          const group = new L.featureGroup();
          L.geoJSON(data).addTo(group);
          map.fitBounds(group.getBounds().pad(0.1));
        }
      })
      .catch(error => {
        console.error('Error loading GeoJSON:', error);
        document.getElementById('loading').innerHTML = 'Error loading data: ' + error.message;
      });
  </script>
</body>
</html>
