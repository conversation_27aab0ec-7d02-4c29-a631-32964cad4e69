<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Singapore Rail Data Preview</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
          crossorigin=""/>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #map {
            height: 100vh;
            width: 100%;
        }
        
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            max-width: 300px;
        }
        
        .legend {
            margin-top: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border: 1px solid #ccc;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 2000;
        }
    </style>
</head>
<body>
    <div id="loading" class="loading">Loading Singapore Rail Data...</div>
    
    <div id="map"></div>
    
    <div class="info-panel">
        <h3>Singapore Rail Data</h3>
        <div id="feature-count">Loading...</div>
        <div class="legend">
            <h4>Legend</h4>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ff4444; border-radius: 50%;"></div>
                <span>Stations (Points)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #4444ff;"></div>
                <span>Rail Lines (LineStrings)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: rgba(68, 255, 68, 0.3); border: 2px solid #44ff44;"></div>
                <span>Areas (Polygons)</span>
            </div>
        </div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" 
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" 
            crossorigin=""></script>

    <script>
        // Initialize the map centered on Singapore
        const map = L.map('map').setView([1.3521, 103.8198], 11);

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19
        }).addTo(map);

        // Counters for different feature types
        let pointCount = 0;
        let lineCount = 0;
        let polygonCount = 0;

        // Style functions for different geometry types
        function getPointStyle(feature) {
            // Color code by station line color if available
            const stationColor = feature.properties.station_colors;
            let color = '#ff4444'; // default red
            
            if (stationColor) {
                switch(stationColor.toLowerCase()) {
                    case 'red': color = '#ff0000'; break;
                    case 'green': color = '#00ff00'; break;
                    case 'blue': color = '#0000ff'; break;
                    case 'yellow': color = '#ffff00'; break;
                    case 'purple': color = '#800080'; break;
                    case 'brown': color = '#8B4513'; break;
                    case 'orange': color = '#FFA500'; break;
                    default: color = '#ff4444';
                }
            }
            
            return {
                radius: 6,
                fillColor: color,
                color: '#000',
                weight: 1,
                opacity: 1,
                fillOpacity: 0.8
            };
        }

        function getLineStyle(feature) {
            return {
                color: '#4444ff',
                weight: 3,
                opacity: 0.8
            };
        }

        function getPolygonStyle(feature) {
            return {
                fillColor: '#44ff44',
                weight: 2,
                opacity: 1,
                color: '#44ff44',
                fillOpacity: 0.3
            };
        }

        // Function to create popup content
        function createPopupContent(feature) {
            const props = feature.properties;
            let content = '<div style="max-width: 200px;">';
            
            if (props.name) {
                content += `<h4>${props.name}</h4>`;
            }
            
            if (props.station_codes) {
                content += `<p><strong>Station Code:</strong> ${props.station_codes}</p>`;
            }
            
            if (props.station_colors) {
                content += `<p><strong>Line Color:</strong> ${props.station_colors}</p>`;
            }
            
            if (props.network) {
                content += `<p><strong>Network:</strong> ${props.network}</p>`;
            }
            
            if (props.mode) {
                content += `<p><strong>Mode:</strong> ${props.mode}</p>`;
            }
            
            content += `<p><strong>Geometry:</strong> ${feature.geometry.type}</p>`;
            content += `<p><strong>ID:</strong> ${feature.id}</p>`;
            content += '</div>';
            
            return content;
        }

        // Load and display the GeoJSON data
        fetch('./data/v1/sg-rail.geojson')
            .then(response => response.json())
            .then(data => {
                // Hide loading indicator
                document.getElementById('loading').style.display = 'none';
                
                // Add GeoJSON layer with custom styling
                L.geoJSON(data, {
                    pointToLayer: function (feature, latlng) {
                        pointCount++;
                        return L.circleMarker(latlng, getPointStyle(feature));
                    },
                    style: function(feature) {
                        if (feature.geometry.type === 'LineString') {
                            lineCount++;
                            return getLineStyle(feature);
                        } else if (feature.geometry.type === 'Polygon') {
                            polygonCount++;
                            return getPolygonStyle(feature);
                        }
                    },
                    onEachFeature: function(feature, layer) {
                        // Add popup with feature information
                        layer.bindPopup(createPopupContent(feature));
                        
                        // Add hover effect
                        layer.on('mouseover', function(e) {
                            if (feature.geometry.type !== 'Point') {
                                layer.setStyle({
                                    weight: 5,
                                    opacity: 1
                                });
                            }
                        });
                        
                        layer.on('mouseout', function(e) {
                            if (feature.geometry.type === 'LineString') {
                                layer.setStyle(getLineStyle(feature));
                            } else if (feature.geometry.type === 'Polygon') {
                                layer.setStyle(getPolygonStyle(feature));
                            }
                        });
                    }
                }).addTo(map);
                
                // Update feature count display
                document.getElementById('feature-count').innerHTML = `
                    <strong>Features:</strong><br>
                    Points: ${pointCount}<br>
                    Lines: ${lineCount}<br>
                    Polygons: ${polygonCount}<br>
                    Total: ${pointCount + lineCount + polygonCount}
                `;
                
                // Fit map to show all features
                if (data.features && data.features.length > 0) {
                    const group = new L.featureGroup();
                    L.geoJSON(data).addTo(group);
                    map.fitBounds(group.getBounds().pad(0.1));
                }
            })
            .catch(error => {
                console.error('Error loading GeoJSON:', error);
                document.getElementById('loading').innerHTML = 'Error loading data: ' + error.message;
            });
    </script>
</body>
</html>
