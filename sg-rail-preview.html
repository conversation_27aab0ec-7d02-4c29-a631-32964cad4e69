<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Singapore Rail Data Preview</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" 
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" 
          crossorigin=""/>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }
        
        #map {
            height: 100vh;
            width: 100%;
        }
        
        .info-panel {
            position: absolute;
            top: 10px;
            right: 10px;
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 999;
            max-width: 300px;
        }

        .legend {
            margin-top: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border: 1px solid #ccc;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 2000;
        }

        .station-label, .exit-label {
            background: none !important;
            border: none !important;
        }

        .labels-control {
            position: absolute;
            top: 120px;
            right: 10px;
            background: white;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1001;
            font-size: 12px;
        }

        .labels-control label {
            display: block;
            margin: 5px 0;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div id="loading" class="loading">Loading Singapore Rail Data...</div>
    
    <div id="map"></div>
    
    <div class="labels-control">
        <h4>Labels</h4>
        <label>
            <input type="checkbox" id="station-labels" checked> Station Names
        </label>
        <label>
            <input type="checkbox" id="exit-labels" checked> Exit Codes
        </label>
    </div>

    <div class="info-panel">
        <h3>Singapore Rail Data</h3>
        <div id="feature-count">Loading...</div>
        <div class="legend">
            <h4>Legend</h4>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #E2231A; border-radius: 50%; border: 2px solid white;"></div>
                <span>Single-line stations</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #009645; border-radius: 50%; border: 3px solid #FFD700;"></div>
                <span>Interchange stations (gold border)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #666666; border-radius: 50%; border: 1px solid white;"></div>
                <span>Station exits (smaller, gray)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: rgba(162, 59, 114, 0.2); border: 1px solid #A23B72;"></div>
                <span>Areas/Infrastructure</span>
            </div>
            <div style="margin-top: 10px; font-size: 12px;">
                <strong>Line Colors (from data):</strong><br>
                <div style="display: flex; flex-wrap: wrap; gap: 3px; margin-top: 5px;">
                    <span style="background: #FF4500; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">NS (Red)</span>
                    <span style="background: #3CB371; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">EW (Green)</span>
                    <span style="background: #8B008B; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">NE (Purple)</span>
                    <span style="background: #FFA500; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">CC (Orange)</span>
                    <span style="background: #483D8B; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">DT (Blue)</span>
                    <span style="background: #8B4513; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">TE (Brown)</span>
                    <span style="background: #9CA3A3; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px;">LRT (Gray)</span>
                </div>
                <div style="margin-top: 8px; font-size: 11px; color: #666;">
                    <strong>Station Priority:</strong> Interchange stations show the color of the highest priority line (NS > EW > DT > NE > CC > TE > LRT)
                </div>
            </div>
        </div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" 
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" 
            crossorigin=""></script>

    <script>
        // Initialize the map centered on Singapore
        const map = L.map('map').setView([1.3521, 103.8198], 11);

        // Base layer options for better data analysis
        const baseLayers = {
            'Light (CartoDB)': L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: 'abcd',
                maxZoom: 20
            }),
            'Dark (CartoDB)': L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: 'abcd',
                maxZoom: 20
            }),
            'Minimal (CartoDB)': L.tileLayer('https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors © <a href="https://carto.com/attributions">CARTO</a>',
                subdomains: 'abcd',
                maxZoom: 20
            }),
            'Satellite (Esri)': L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles © Esri — Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
                maxZoom: 20
            })
        };

        // Add default light layer
        baseLayers['Light (CartoDB)'].addTo(map);

        // Counters for different feature types
        let stationCount = 0;
        let exitCount = 0;
        let lineCount = 0;
        let polygonCount = 0;

        // Layer groups for labels
        let stationLabels = L.layerGroup().addTo(map);
        let exitLabels = L.layerGroup().addTo(map);

        // Function to load GeoJSON data
        async function loadGeoJSONData() {
            try {
                const response = await fetch('./data/v1/sg-rail.geojson');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                // If fetch fails (likely due to CORS), try to serve via a simple HTTP server
                throw new Error('Failed to load GeoJSON file. Please serve this HTML file via HTTP server (not file://) or use the embedded version.');
            }
        }

        // Enhanced color palette for Singapore MRT lines
        const lineColors = {
            'red': '#E2231A',      // North South Line
            'green': '#009645',    // East West Line
            'purple': '#9900AA',   // North East Line
            'yellow': '#FA9E0D',   // Circle Line
            'blue': '#0099AA',     // Downtown Line
            'brown': '#9D5B25',    // Thomson-East Coast Line
            'orange': '#FF6319',   // Future lines
            'pink': '#E899CD',     // Future lines
            'grey': '#9CA3A3',     // Default/unknown
            'gray': '#9CA3A3',     // LRT lines
            // CSS color names from the actual data
            'darkslateblue': '#483D8B',    // Downtown Line
            'darkmagenta': '#8B008B',      // North East Line
            'orangered': '#FF4500',        // North South Line
            'saddlebrown': '#8B4513',      // Thomson-East Coast Line
            'mediumseagreen': '#3CB371',   // East West Line
            'orange': '#FFA500'            // Circle Line
        };

        // Style functions for different geometry types
        function getPointStyle(feature) {
            const stationColors = feature.properties.station_colors;
            let colors = [];

            if (stationColors) {
                // Handle multi-line stations (e.g., "blue-yellow", "red-purple-yellow")
                const colorList = stationColors.split('-').filter(color => color.trim() !== '');
                // Remove duplicates (like "green-green")
                const uniqueColors = [...new Set(colorList)];
                colors = uniqueColors.map(color => lineColors[color.toLowerCase()] || lineColors.grey);
            }

            if (colors.length === 0) {
                colors = [lineColors.grey];
            }

            // For multi-line stations, create a more sophisticated color scheme
            const isInterchange = colors.length > 1;
            let primaryColor = colors[0];

            // Special handling for major interchanges - use a distinctive color
            if (isInterchange) {
                // For interchanges, use a gradient-like approach or the most prominent line
                // Priority order: red (NS) > green (EW) > blue (DT) > purple (NE) > yellow (CC) > brown (TE)
                const priorityOrder = [
                    lineColors.red, lineColors.green, lineColors.blue,
                    lineColors.purple, lineColors.yellow, lineColors.brown
                ];

                for (const priorityColor of priorityOrder) {
                    if (colors.includes(priorityColor)) {
                        primaryColor = priorityColor;
                        break;
                    }
                }
            }

            return {
                radius: isInterchange ? 10 : 8,
                fillColor: primaryColor,
                color: isInterchange ? '#FFD700' : '#ffffff', // Gold border for interchanges
                weight: isInterchange ? 3 : 2,
                opacity: 1,
                fillOpacity: 0.9
            };
        }

        function getLineStyle(feature) {
            const props = feature.properties;
            let color = '#2E86AB'; // Default blue

            // Use line_color property from the data
            if (props.line_color) {
                color = lineColors[props.line_color.toLowerCase()] || props.line_color;
            }

            return {
                color: color,
                weight: 4,
                opacity: 0.8,
                lineCap: 'round',
                lineJoin: 'round'
            };
        }

        function getPolygonStyle(feature) {
            return {
                fillColor: '#A23B72',
                weight: 1,
                opacity: 0.8,
                color: '#A23B72',
                fillOpacity: 0.2
            };
        }

        // Style function for exit markers (smaller, different appearance)
        function getExitStyle(feature) {
            return {
                radius: 4, // Smaller than stations
                fillColor: '#666666', // Gray color
                color: '#ffffff', // White border
                weight: 1,
                opacity: 0.8,
                fillOpacity: 0.7
            };
        }

        // Function to create popup content
        function createPopupContent(feature) {
            const props = feature.properties;
            let content = '<div style="max-width: 250px;">';

            if (props.name) {
                content += `<h4 style="margin: 0 0 10px 0;">${props.name}</h4>`;
            }

            if (props.station_codes) {
                const codes = props.station_codes.split('-');
                content += `<p><strong>Station Code(s):</strong> `;
                content += codes.map(code => `<span style="background: #f0f0f0; padding: 2px 6px; border-radius: 3px; margin: 2px;">${code}</span>`).join(' ');
                content += `</p>`;
            }

            if (props.station_colors) {
                const colors = props.station_colors.split('-');
                content += `<p><strong>Line(s):</strong> `;
                colors.forEach(color => {
                    const colorCode = lineColors[color.toLowerCase()] || '#ccc';
                    content += `<span style="background: ${colorCode}; color: white; padding: 2px 8px; border-radius: 3px; margin: 2px; display: inline-block;">${color.toUpperCase()}</span>`;
                });
                content += `</p>`;
            }

            if (props.line_color) {
                const colorCode = lineColors[props.line_color.toLowerCase()] || props.line_color;
                content += `<p><strong>Line Color:</strong> <span style="background: ${colorCode}; color: white; padding: 2px 8px; border-radius: 3px;">${props.line_color}</span></p>`;
            }

            if (props.network) {
                content += `<p><strong>Network:</strong> ${props.network}</p>`;
            }

            if (props.mode) {
                content += `<p><strong>Mode:</strong> ${props.mode}</p>`;
            }

            content += `<p><strong>Type:</strong> ${feature.geometry.type}</p>`;
            content += `<p style="font-size: 11px; color: #666;"><strong>ID:</strong> ${feature.id}</p>`;
            content += '</div>';

            return content;
        }

        // Load and display the GeoJSON data
        // Embedding data directly to avoid CORS issues with local files
        loadGeoJSONData()
            .then(data => {
                // Hide loading indicator
                document.getElementById('loading').style.display = 'none';
                
                // Separate features by geometry type and stop_type for proper layering
                const lineFeatures = data.features.filter(f => f.geometry.type === 'LineString' || f.geometry.type === 'MultiLineString');
                const polygonFeatures = data.features.filter(f => f.geometry.type === 'Polygon');
                const exitFeatures = data.features.filter(f => f.geometry.type === 'Point' && f.properties.stop_type === 'entrance');
                const stationFeatures = data.features.filter(f => f.geometry.type === 'Point' && f.properties.stop_type === 'station');

                // Add layers in order: polygons (bottom), lines, exits, stations (top)

                // 1. Add polygon layer (areas/infrastructure)
                if (polygonFeatures.length > 0) {
                    L.geoJSON({type: "FeatureCollection", features: polygonFeatures}, {
                        style: function(feature) {
                            polygonCount++;
                            return getPolygonStyle(feature);
                        },
                        onEachFeature: function(feature, layer) {
                            layer.bindPopup(createPopupContent(feature));
                        }
                    }).addTo(map);
                }

                // 2. Add line layer (rail lines)
                if (lineFeatures.length > 0) {
                    L.geoJSON({type: "FeatureCollection", features: lineFeatures}, {
                        style: function(feature) {
                            lineCount++;
                            return getLineStyle(feature);
                        },
                        onEachFeature: function(feature, layer) {
                            layer.bindPopup(createPopupContent(feature));

                            // Add hover effect for lines
                            layer.on('mouseover', function(e) {
                                if (feature.geometry.type === 'LineString' || feature.geometry.type === 'MultiLineString') {
                                    layer.setStyle({
                                        weight: 6,
                                        opacity: 1
                                    });
                                }
                            });

                            layer.on('mouseout', function(e) {
                                if (feature.geometry.type === 'LineString' || feature.geometry.type === 'MultiLineString') {
                                    layer.setStyle(getLineStyle(feature));
                                }
                            });
                        }
                    }).addTo(map);
                }

                // 3. Add exit layer (station exits) - below stations
                if (exitFeatures.length > 0) {
                    L.geoJSON({type: "FeatureCollection", features: exitFeatures}, {
                        pointToLayer: function (feature, latlng) {
                            exitCount++;
                            const marker = L.circleMarker(latlng, getExitStyle(feature));

                            // Add text label for exit
                            if (feature.properties.name) {
                                const label = L.divIcon({
                                    className: 'exit-label',
                                    html: `<div style="font-size: 10px; color: #666; font-weight: bold; text-shadow: 1px 1px 1px white; pointer-events: none;">${feature.properties.name}</div>`,
                                    iconSize: [20, 10],
                                    iconAnchor: [10, -5]
                                });
                                L.marker(latlng, {icon: label}).addTo(exitLabels);
                            }

                            return marker;
                        },
                        onEachFeature: function(feature, layer) {
                            layer.bindPopup(createPopupContent(feature));

                            // Add hover effect for exits
                            layer.on('mouseover', function(e) {
                                layer.setStyle({
                                    radius: 6,
                                    weight: 2
                                });
                            });

                            layer.on('mouseout', function(e) {
                                layer.setStyle(getExitStyle(feature));
                            });
                        }
                    }).addTo(map);
                }

                // 4. Add station layer (stations) - on top
                if (stationFeatures.length > 0) {
                    L.geoJSON({type: "FeatureCollection", features: stationFeatures}, {
                        pointToLayer: function (feature, latlng) {
                            stationCount++;
                            const marker = L.circleMarker(latlng, getPointStyle(feature));

                            // Add text label for station
                            if (feature.properties.name) {
                                const isInterchange = feature.properties.station_colors && feature.properties.station_colors.includes('-');
                                const label = L.divIcon({
                                    className: 'station-label',
                                    html: `<div style="font-size: 11px; color: #333; font-weight: bold; text-shadow: 1px 1px 2px white; pointer-events: none; text-align: center;">${feature.properties.name}</div>`,
                                    iconSize: [100, 12],
                                    iconAnchor: [50, -15]
                                });
                                L.marker(latlng, {icon: label}).addTo(stationLabels);
                            }

                            return marker;
                        },
                        onEachFeature: function(feature, layer) {
                            layer.bindPopup(createPopupContent(feature));

                            // Add hover effect for stations
                            layer.on('mouseover', function(e) {
                                const currentStyle = getPointStyle(feature);
                                layer.setStyle({
                                    radius: currentStyle.radius + 2,
                                    weight: currentStyle.weight + 1
                                });
                            });

                            layer.on('mouseout', function(e) {
                                layer.setStyle(getPointStyle(feature));
                            });
                        }
                    }).addTo(map);
                }

                // Add layer control for base maps
                L.control.layers(baseLayers).addTo(map);

                // Update feature count display
                document.getElementById('feature-count').innerHTML = `
                    <strong>Features:</strong><br>
                    Stations: ${stationCount}<br>
                    Exits: ${exitCount}<br>
                    Lines: ${lineCount}<br>
                    Polygons: ${polygonCount}<br>
                    Total: ${stationCount + exitCount + lineCount + polygonCount}
                `;
                
                // Fit map to show all features
                if (data.features && data.features.length > 0) {
                    const group = new L.featureGroup();
                    L.geoJSON(data).addTo(group);
                    map.fitBounds(group.getBounds().pad(0.1));
                }
            })
            .catch(error => {
                console.error('Error loading GeoJSON:', error);
                document.getElementById('loading').innerHTML = 'Error loading data: ' + error.message;
            });

        // Add label toggle functionality
        document.getElementById('station-labels').addEventListener('change', function(e) {
            if (e.target.checked) {
                map.addLayer(stationLabels);
            } else {
                map.removeLayer(stationLabels);
            }
        });

        document.getElementById('exit-labels').addEventListener('change', function(e) {
            if (e.target.checked) {
                map.addLayer(exitLabels);
            } else {
                map.removeLayer(exitLabels);
            }
        });
    </script>
</body>
</html>
