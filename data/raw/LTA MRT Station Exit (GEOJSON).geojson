{"type": "FeatureCollection", "crs": {"type": "name", "properties": {"name": "urn:ogc:def:crs:OGC:1.3:CRS84"}}, "features": [{"type": "Feature", "properties": {"Name": "kml_1", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KAKI BUKIT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D3065772B4A8884B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.909146276263, 1.33492189034272, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_2", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEDOK RESERVOIR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D9EF7EDA7B6AE00E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.933487189855, 1.33655490332863, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_3", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BENCOOLEN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B84D682A36C94724</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.849271703873, 1.29769911765443, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_4", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BENCOOLEN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>58C4E2D41BF56E13</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.850843384717, 1.29919519603019, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_5", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KAKI BUKIT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C7859F14611E3E46</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.909405098626, 1.33531120636956, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_6", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES WEST MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>538DEFEDB8DF426A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.938948503782, 1.34499919751448, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_7", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>JALAN BESAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1DF95F048432E540</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.855296125414, 1.30488989412929, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_8", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BENDEMEER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CC5340F062D36648</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.863074005937, 1.31385250562225, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_9", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEDOK NORTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7227ED6E51A0A478</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.917810526029, 1.33442257655151, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_10", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UPPER CHANGI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DCF272AB67E90046</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.961043080709, 1.34084047406433, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_11", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GEYLANG BAHRU MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7D14D8F944626336</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.871794271427, 1.32110911579532, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_12", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1D91E52F1D3C6192</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.943678182032, 1.35487057481792, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_13", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit G</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4857FF7FA81FC41A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.942723496695, 1.35459633872811, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_14", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEDOK RESERVOIR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2B5A7B2D633B3BD5</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.932886822506, 1.33634776089978, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_15", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SENJA LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C46382A21331EA45</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.762325766572, 1.3828754471752, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_16", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YISHUN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C2D55B079B8AA456</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.834865533461, 1.42963629191008, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_17", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YISHUN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C551A20E6E342A65</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.835643654933, 1.42966559716462, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_18", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SERANGOON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit G</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>40A841F72D73A02C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.872866695396, 1.35079019941389, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_19", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SERANGOON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DA4475EAB2A9601C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.873641321904, 1.34983373825613, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_20", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SERANGOON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>12758F1DDC8CF6B8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.872902116341, 1.3508202454304, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_21", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAI SENG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>54874BD41781B357</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.887627437262, 1.33591408492645, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_22", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>EUNOS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CB562FEDBA333355</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.903215186978, 1.31952991880127, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_23", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SEMBAWANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CBDA244C9326B8C6</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.819758966004, 1.44915683716069, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_24", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HOUGANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6D19186EFE5FD5FB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.892972400972, 1.37194611655273, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_25", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHANGI AIRPORT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2D491EF64CB58C02</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.989276642414, 1.3563414210048, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_26", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>OUTRAM PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 8</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3057E9135BFBD0F9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.838117129674, 1.28232163095245, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_27", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>OUTRAM PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 7</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3C9A35B7FDC33A1A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.838640195189, 1.2810220177189, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_28", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MAXWELL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CE1632BF9D6E1F72</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.842916305543, 1.28019152238094, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_29", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MAXWELL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5D6CDC9708168E3B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.844164413475, 1.28103425526259, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_30", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MAXWELL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>95F61602B7CE905C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.844895459262, 1.28091943659091, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_31", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SHENTON WAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AAEEB32398817172</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851835707382, 1.27746205833565, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_32", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SHENTON WAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>03444FA2BC9AF9DF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851807978051, 1.2771436461189, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_33", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SHENTON WAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2B1F698DD3DB8746</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851034086049, 1.27765594176983, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_34", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SHENTON WAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FD720BE5E5565E2E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.850915094628, 1.27700305442423, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_35", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SHENTON WAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A54D185A5569B115</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.850128771071, 1.27762958112669, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_36", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SHENTON WAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 6</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>42CEFA5FE7DD78D9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.849893223277, 1.27792920509497, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_37", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARINA BAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4F0EDD7252D1EAF8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.854470032954, 1.27735303297793, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_38", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARINA BAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>03BC8BA7B72C2D81</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.854319501956, 1.27469326445441, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_39", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARINA BAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F125CE014C37C2DB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.856488836263, 1.27363420910955, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_40", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARINA BAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2F2639EB423EFD0D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.85537174149, 1.27541636589719, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_41", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 11</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0A847CDFAB7B3F63</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.830496723547, 1.30421797905404, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_42", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 10</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B3BE3F499ED707F1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.831126574248, 1.30404570543486, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_43", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 9</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AD31C99F12BAEE46</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.831506861841, 1.30366551621641, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_44", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 8</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>705858431FF72BCB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.83178613556, 1.30344571958931, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_45", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 13</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DBD31FCCFBAE692E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832124829631, 1.30229921132352, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_46", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FCA5411E28B2A33D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832023815157, 1.30394471745993, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_47", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 6</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D7A5F13EF395CD85</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832320915154, 1.30364769526593, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_48", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>STEVENS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3DDAD01108E1ED83</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.826322577145, 1.32093774341441, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_49", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>STEVENS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>93AA974193E59BF8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.825882869659, 1.32151990425561, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_50", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>STEVENS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>43C1FAB4A764D619</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.825728378177, 1.31995163510642, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_51", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>STEVENS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A6EA6AA10E329D97</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.826013593581, 1.31882889618825, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_52", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>STEVENS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FD76A5DA4056C137</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.824991570643, 1.31947046099515, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_53", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HAVELOCK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>43806860EF6F95B6</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833839753358, 1.28667674481792, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_54", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HAVELOCK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2B4107CDD54FF45A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833495118232, 1.28694406698765, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_55", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HAVELOCK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>41415B22F604DBB1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833961901637, 1.28956096428231, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_56", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HAVELOCK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F01A3E97C6F40606</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.834003496172, 1.29016689416429, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_57", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CITY HALL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0D2982825A7A63D7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851802333809, 1.29345482595132, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_58", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SOO TECK LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5C840B0088A042B7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.897029921817, 1.40501492486707, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_59", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SOO TECK LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7CDEC183A92283AA</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.897408572121, 1.40524489949914, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_60", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DAMAI LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D50CD0EB8DF1A6AD</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.908792226992, 1.40528461343374, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_61", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARINA BAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>9B69D26308E8046F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.85500333792, 1.27615893131623, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_62", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GARDENS BY THE BAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>139A309B16435395</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.867154122142, 1.27830343996812, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_63", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GARDENS BY THE BAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>64955697073C7D02</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.867968767276, 1.27882622333397, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_64", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GARDENS BY THE BAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>272B01765238D930</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.868586734111, 1.28037075595564, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_65", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARINA SOUTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>20846F5C306F5CFE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.861503878728, 1.27357480448593, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_66", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARINA SOUTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AC98AC5E6E4A2440</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.861777210111, 1.27385994951971, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_67", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARINA SOUTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0D8225B43A9DB12A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.862894304629, 1.27313520508679, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_68", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARINA SOUTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>465066CFAE562319</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.862668508874, 1.27551141461164, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_69", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD BOULEVARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>387A884B34A26CED</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.823637898436, 1.30362395339039, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_70", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD BOULEVARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4FC444363DB7E86D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.82389561212, 1.30246332428451, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_71", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GREAT WORLD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>65268329B5AD5F93</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833405504371, 1.29655259456474, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_72", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GREAT WORLD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FCF6BA18E63CA65C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833904631248, 1.29332097607264, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_73", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GREAT WORLD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>72FD2496EC6B1D54</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832627103189, 1.2928694999213, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_74", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GREAT WORLD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 6</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4F2D7457D45F29D1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832841014649, 1.29318434464758, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_75", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GREAT WORLD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C2D22AD994AA8975</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832734059176, 1.29381997655443, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_76", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GREAT WORLD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5FD7E726E04576E5</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833037100577, 1.29602983278925, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_77", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HAVELOCK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3509B8FC37977C9F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833369520278, 1.28884356335354, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_78", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>NAPIER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8800C596B7DB260F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.819483777776, 1.30693598792234, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_79", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>NAPIER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CE1402EDB6926C4A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.819506621185, 1.3063449793684, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_80", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 12</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A360078B6432F773</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.830663098953, 1.30285761474898, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_81", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>OASIS LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0BEB37C242EBF30C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.912646133061, 1.40250713144757, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_82", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KADALOOR LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8F348D1DC2C6DCD9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.916387458482, 1.39980926315018, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_83", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MERIDIAN LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FCD25ADA46274E53</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.908965833187, 1.39668105013866, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_84", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MERIDIAN LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CB5492FBE757562B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.908869496154, 1.39712168979943, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_85", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>COVE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C06D10E6EBF83EA6</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.905928735776, 1.399124393509, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_86", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SOUTH VIEW LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>335EFAECE777F0ED</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.74510113108, 1.38031765699103, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_87", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HOLLAND VILLAGE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>76D46E22BDCAEAAA</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.79576910839, 1.31106948365962, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_88", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ONE-NORTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>45A9A4D683B771BF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.787927806168, 1.30035949347946, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_89", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>OUTRAM PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>00AC1131DC50AE35</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.83978923248, 1.28099327615611, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_90", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DOVER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3350D5255C75B2ED</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.778304715288, 1.31141543344877, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_91", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DOVER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F180ED4FDDA94062</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.778464771304, 1.31169319167964, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_92", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BOON LAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A277212C952A5DE9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.705650038035, 1.33862950779597, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_93", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BOON LAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A16B7FAC4E5AB117</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.7053533532, 1.3385141209312, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_94", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BOON LAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0E296E75C6372829</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.705795031365, 1.33827322197702, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_95", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT BATOK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>474392E67F3BCE9B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.749936955579, 1.34938958776808, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_96", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>JOO KOON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AFF1FA0853BEBB53</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.678520709011, 1.32750208445865, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_97", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>JOO KOON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>93D34F9CC519E4F1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.678644077077, 1.32795611143786, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_98", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHOA CHU KANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B98A2C3B9DA5DE50</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.744223583926, 1.38570539067001, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_99", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KRANJI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>EFEBDF844A7E944C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.762496770529, 1.42512495935501, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_100", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARSILING MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0449B4DEFBEC025F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.774410520697, 1.43275137172006, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_101", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARSILING MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AC661F5F30DEBD6A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.773997133375, 1.43275136098458, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_102", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HARBOURFRONT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FF2613B98FF1665F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.8215802917, 1.26497195959122, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_103", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HARBOURFRONT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>690B8B15B6CB2137</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.821398857154, 1.26597925499591, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_104", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HARBOURFRONT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8A673F2CF4ABB497</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.820468350461, 1.26601318319783, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_105", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHINATOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3A1CC9F9DAB5860F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.8437978881, 1.28371277043019, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_106", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CLARKE QUAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>30B1E5E1C8017C54</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.845903432736, 1.28780575265584, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_107", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CLARKE QUAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit G</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>804AAB0D2993008D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.28919925781809, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_108", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LITTLE INDIA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>42434AF40AAA3CAF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.84936002946, 1.30560525103922, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_109", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SAM KEE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>ED444F6994E770B0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.40982767244172, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_110", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DAMAI LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C9AD817C66E8F720</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.908613856028, 1.40504602830874, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_111", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>OASIS LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>9B8770AEDF254A19</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.912754573795, 1.4021037193748, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_112", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KADALOOR LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F84C17D9498B88F1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.916517130328, 1.39941146718039, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_113", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RIVIERA LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B302D78A0F102EF5</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.915935470521, 1.39455906596969, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_114", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RIVIERA LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>720E105DE8EACE66</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.916263098527, 1.3943256111184, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_115", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CORAL EDGE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E7718F39AA487D8B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.912708744367, 1.39397080711083, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_116", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CORAL EDGE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D4CB376FFBED3D8A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.91265223397, 1.39368465222162, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_117", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>COVE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8007BDF5105E0800</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.9061760136, 1.39930265660882, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_118", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HARBOURFRONT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>54F69FDA2233B8B4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.822509807219, 1.26508503461184, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_119", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MACPHERSON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AB2B8B46B299062B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204409</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.889793336526, 1.3266787588073, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_120", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MACPHERSON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A4023956A12A2155</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.890665675221, 1.32585503018196, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_121", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MACPHERSON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DA5558C22F66CC7C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.890262565574, 1.32548152990651, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_122", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TONGKANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F83D29999DC4CC0E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.886084357383, 1.38937900794855, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_123", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TONGKANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DB46A51FA82F6865</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.885962114119, 1.38918016099808, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_124", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RENJONG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>35048F84F3D858BC</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.890409161732, 1.38669795588852, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_125", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RENJONG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B0178F7E6DD16A1A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.890438192124, 1.38681414421555, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_126", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RANGGUNG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>077813950167F0B1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.897321723817, 1.3841763860699, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_127", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RANGGUNG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8D5E7FC2B8D737C3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.897223938055, 1.38410488340512, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_128", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KANGKAR LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8F3E5F0D6E718156</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.902147558141, 1.38407450287348, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_129", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KANGKAR LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>09AD446CDB37F542</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.902276368029, 1.38376520316725, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_130", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BAKAU LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8A3C1E7317417FD2</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.905445630714, 1.38818538545215, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_131", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BAKAU LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FD691A27B9222ACC</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.905482124043, 1.387840799257, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_132", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RUMBIA LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>19F50D0B45ED2625</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.906018791652, 1.39134048641742, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_133", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RUMBIA LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>24270D468CB0C551</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.906105819366, 1.39141424178713, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_134", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ONE-NORTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1C5F562480779031</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.786907466321, 1.29902749872281, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_135", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KENT RIDGE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3A15EBE767637D7E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.784809631882, 1.29242811590855, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_136", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KENT RIDGE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D5D0496822FDF5DF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.784465895365, 1.29431588700949, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_137", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HAW PAR VILLA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F33EEDAA74A75803</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.782006397072, 1.28305527354176, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_138", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PASIR PANJANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DC64D55F9888B200</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.79203018692, 1.27600323278505, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_139", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LABRADOR PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>77D4BE63E9419638</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204434</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.802355553663, 1.27210695805434, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_140", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TELOK BLANGAH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A4D239AA865A436A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.80988373092, 1.2707212262114, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_141", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAN KAH KEE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>90A659920312F326</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.807799119073, 1.32533306908018, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_142", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CASHEW MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CC93934ACEAF445A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.764234480711, 1.36983331859629, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_143", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SIXTH AVENUE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2C20FEBDCFC154A5</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.796566082121, 1.33078046994618, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_144", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KING ALBERT PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E65E02F76E0D2687</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.783030260215, 1.33553707766976, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_145", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HILLVIEW MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A858C4A736160A26</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.767108459936, 1.36208433991773, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_146", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT PANJANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>73796FEF5F3AC744</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.761922934209, 1.37900796974076, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_147", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ROCHOR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3D9484B6A1206900</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.852375021633, 1.30340410653552, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_148", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DOWNTOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>EF75BE606A770937</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.852427746441, 1.27979623969206, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_149", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TELOK AYER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>974D41D519377A08</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.848506543896, 1.28188738923558, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_150", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PIONEER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2362DB8619A766E8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.697155373411, 1.33734462692044, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_151", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PIONEER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>72551C3253621E4C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.697129452876, 1.33783162800548, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_152", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TANJONG PAGAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit I</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>80D1390DC9F96913</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.845656003885, 1.27644796791678, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_153", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUONA VISTA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D90F93DC24A3A902</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.790930065154, 1.30629814162477, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_154", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>COMPASSVALE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7A6540195C75D6C2</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.9006164696, 1.39432752151004, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_155", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>COMPASSVALE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5E01800475DDF5ED</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.900706597553, 1.39453417229989, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_156", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PUNGGOL POINT LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>45E35F4903E99C73</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.906693304924, 1.41666406794097, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_157", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PUNGGOL POINT LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A61E18152335BFC3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.906817792333, 1.41691476791429, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_158", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>NIBONG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>853F2A40C64D662F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.900073808053, 1.4117727733571, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_159", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>NIBONG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>BDB536DD4D5059E3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.900531554207, 1.4119103237023, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_160", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SUMANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DE121A01C547F972</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.898335680078, 1.40836843102047, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_161", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SUMANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5B2417A2FAFF4D06</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.898757123956, 1.40848790183837, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_162", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DOWNTOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8938BEADE9C163A4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.852478046399, 1.27952791063231, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_163", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DOWNTOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>419B0A25DCF2D8F3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.85290812847, 1.27960131828225, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_164", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DOWNTOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D77ADD7D0B7369D2</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.853152091961, 1.27944436839161, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_165", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DOWNTOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2397863DFE9A27AE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.853013759131, 1.27917604078089, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_166", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TELOK AYER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F628F180D146397C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.848841656061, 1.28225066426407, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_167", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARRER PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit I</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>29BDA63F18A13487</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.855806657255, 1.3117658772962, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_168", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARRER PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit H</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>54174A49E0822F8A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.856098852115, 1.31188350806091, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_169", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BOON KENG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B8343022B9AB9000</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.860706833288, 1.31791481647231, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_170", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>POTONG PASIR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1B39543BA5308563</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.868966444703, 1.33280135224287, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_171", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>NICOLL HIGHWAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2E07D7422B61952A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.864151747702, 1.29966075968377, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_172", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BOTANIC GARDENS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A2B2C9A4721A3198</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.814976905734, 1.32253463294936, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_173", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARRER ROAD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>256E821C70941FD4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.807965230708, 1.31749372589551, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_174", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARRER ROAD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C5B7A8210D496AE2</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.807389234558, 1.31755201617223, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_175", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HOLLAND VILLAGE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6C37FA62DE7E9E31</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.795753770113, 1.31071120133272, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_176", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HOLLAND VILLAGE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5AE0992EF2AB9ACA</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.796139102483, 1.31122026577719, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_177", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BAYFRONT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>959F42085F43D0D7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.859596872187, 1.28283490868894, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_178", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BAYFRONT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F8BA0963046FE249</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.859759180227, 1.28270522470828, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_179", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEAUTY WORLD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>91AF290F33622FDB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.775450679441, 1.34158221454984, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_180", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BISHAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CC79D6FD2A8F1DD0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.850201081965, 1.35105321993329, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_181", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BISHAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>32BBD904B9B7DCBD</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.848304991722, 1.35057959871202, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_182", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BOON KENG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0194CF0B3B46824D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.861502357069, 1.32010687599131, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_183", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SENGKANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E026168F5D7B044A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.89570545661, 1.39237084363153, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_184", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SENGKANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7B18AEBD6DA44B02</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.895636894365, 1.39179563766207, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_185", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SENGKANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>245FC278F4E5D3D3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.895405720754, 1.39182911971822, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_186", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SENGKANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5C2ABE67FA9DA403</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.895209399885, 1.39088158609449, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_187", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PUNGGOL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0B2529A684127E11</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.902305996656, 1.40533957622685, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_188", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PUNGGOL LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>ABD1B5F0A5C95A52</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.901798524363, 1.40443833617806, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_189", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PUNGGOL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E3FC8EFB1852284C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.902598196988, 1.40518624176365, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_190", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PUNGGOL LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>946836207101FC23</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.902115639183, 1.40430062572978, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_191", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUANGKOK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>64BE76AD177F77E2</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.893463152237, 1.38264972282021, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_192", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YISHUN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6A53878C399EA119</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.835090842713, 1.42911681431371, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_193", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAI SENG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B121007692C889BF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.888214067772, 1.33462087723075, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_194", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BARTLEY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>21A49043695321F2</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.878908693925, 1.34350106152933, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_195", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BARTLEY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0455856EC60DC1B0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.880255893265, 1.34224874921609, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_196", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SERANGOON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit H</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F904CF22F1692BDE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.873546495734, 1.34972769951436, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_197", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SERANGOON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>56E474B40576C822</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.873957395391, 1.349481680255, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_198", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SERANGOON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AF9B4A2ED6E87545</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.874321946576, 1.3499015921065, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_199", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SERANGOON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>29231A6BE44BC0C3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.873694014498, 1.35059510442143, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_200", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SERANGOON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C1B3F5AC67211832</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.871449340349, 1.35094827793695, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_201", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LORONG CHUAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F22EC9809EB9F243</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.862918299956, 1.35173853791277, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_202", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LORONG CHUAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>28A9382A30C8CE74</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.864933673465, 1.35143910357514, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_203", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARYMOUNT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>00445EC81B4CF09B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.840058918077, 1.34786607217308, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_204", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARYMOUNT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D6F8DA032170A0CA</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.839087956801, 1.34898038642137, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_205", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DHOBY GHAUT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D718E6930765A66B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.844747857191, 1.29964924216132, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_206", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CALDECOTT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B27228AA1D3CABE9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.839406731225, 1.33710531669564, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_207", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>EXPO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FD3508562E82B730</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.961942958226, 1.33532172433891, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_208", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>EXPO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>697283586E726E3F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.962075287596, 1.33525294423695, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_209", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ADMIRALTY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>37905A3114C1A244</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.800533463258, 1.44015239674676, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_210", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ADMIRALTY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>746E9DB47E3B63DD</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.801364534451, 1.4405333880723, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_211", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ANG MO KIO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>65B35A9BB66315DF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.849343273705, 1.3694405305965, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_212", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BAYFRONT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>9446A698C2D12F17</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.85873192474, 1.28096742397164, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_213", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BAYFRONT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2379024BE499AD08</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.858441003652, 1.28110463709187, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_214", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BAYFRONT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D926E2FE3DE5668D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.859493846611, 1.28283435447098, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_215", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B610E91C58D66F87</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.945089563334, 1.35353908166709, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_216", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HOUGANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CAD432D1FEF31915</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.891975029135, 1.37214183483289, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_217", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HOUGANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2D8A3BEA17E41AFD</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.892445723159, 1.37020857826092, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_218", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KOVAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>420650AF4622E265</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.884721732122, 1.36020562306327, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_219", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KOVAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A740F42ACFBB45B6</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.885450578, 1.36002801014446, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_220", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KOVAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AECA4062F6010632</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.884307428636, 1.35970758870125, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_221", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>EUNOS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8EBFC6F8732D28BD</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.902894137842, 1.31951727489935, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_222", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>EUNOS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C83CDC2726150409</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.902582077618, 1.3195625452698, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_223", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KEMBANGAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E09063B99CE6B24F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.912899288746, 1.32115210805545, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_224", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KEMBANGAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>85173EF160C47192</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.912941079407, 1.32092376028202, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_225", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEDOK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>20B9A6B793404FF3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.929157143095, 1.32356863615635, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_226", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEDOK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>29CA710B93C2CB4B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.929673089995, 1.32426834276114, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_227", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ANG MO KIO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>87583D09ABBEFC42</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.849596100655, 1.36968532219972, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_228", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ANG MO KIO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6CB268C777B52269</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.849939000741, 1.3694647065051, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_229", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BISHAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1B5DEBDC60CE4A0E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.84831225598, 1.35106443927374, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_230", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BISHAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>03844339B281C755</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.848432923875, 1.35103483777104, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_231", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BISHAN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2C4E7954EAAC322A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.848209281294, 1.35058491153989, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_232", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRADDELL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>90DD180B16820DA9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.847440830591, 1.34067216394317, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_233", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRADDELL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B8C182EB1FFEA0F7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846406497231, 1.34074431901447, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_234", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRADDELL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6D90E5E9F8684602</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846429540721, 1.34106899206951, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_235", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TOA PAYOH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DE385222B3EA4A3C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.847577567476, 1.33231553664677, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_236", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TOA PAYOH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4C8924227130F1F0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.847319773632, 1.33299410183748, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_237", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>NOVENA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F8D815A9ADA89BE1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.843339733476, 1.31989588603478, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_238", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>NOVENA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1C30DBBBBC9F938C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.844389333871, 1.32109486830236, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_239", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TOA PAYOH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5AB7564CE0D0C509</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846517406156, 1.33327874598633, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_240", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLEIGH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>021A225B04A13444</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.871053804158, 1.3398672199419, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_241", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLEIGH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>390C6CA013A0C83B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.870940604568, 1.33851065441145, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_242", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLEIGH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6AA9CF467B21891C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.870508468145, 1.33858314946013, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_243", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BOON KENG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>437A42A639D5778D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.861903675519, 1.31923519599748, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_244", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>POTONG PASIR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C0620A11315B77FF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.868703893188, 1.33106671097983, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_245", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>POTONG PASIR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>ADC7223B75A5E175</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.869333239363, 1.33114814073399, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_246", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SOMERSET MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C073EA14C7AA013E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.838399326576, 1.3000284884874, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_247", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ONE-NORTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>14396AC4C6B421F3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.787565456204, 1.29971469760634, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_248", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRAS BASAH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>57B5E4CF560CD25A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.850474275507, 1.29604621307724, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_249", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRAS BASAH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1C26490BD624B606</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851327253386, 1.29685892177104, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_250", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRAS BASAH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>9448BEB862765BA4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.850249852705, 1.29701443296395, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_251", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRAS BASAH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D53EA9E82E6BE2E5</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.85015752098, 1.29755148518356, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_252", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRAS BASAH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6010EED2184D4748</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.850423192511, 1.29753545171709, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_253", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ESPLANADE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E51530AE6521B581</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.856305554739, 1.29337054827687, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_254", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT BATOK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C429C4E74FC7D20E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.749766564931, 1.34952006073274, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_255", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHINATOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FCD3F9C4B46D59AD</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.843476210528, 1.28540645998668, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_256", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT BATOK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E14204E5225118FF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.749118394189, 1.34840537590496, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_257", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT BATOK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>400A184FD3289B7A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.749973990258, 1.34956107535244, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_258", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT GOMBAK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>86F244E67822A799</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.751726614847, 1.35906497975184, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_259", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT GOMBAK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>09DD5C2C18DD73D5</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.752076389375, 1.35906984624156, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_260", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT GOMBAK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>ED8947B8EAD1722E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.751950990526, 1.35929785281919, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_261", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BOON LAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D64646CF4B4055D8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.706145547967, 1.33842069565789, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_262", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BOON LAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E67002E356755554</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.705955152611, 1.33873810873339, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_263", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BOON LAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F72B433FA6426C66</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.705408994969, 1.33827998808197, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_264", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LAKESIDE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>577ECF5E707DE228</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.721712827003, 1.34430416996866, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_265", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LAKESIDE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DA7033E23CE2B741</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.721075412743, 1.34408145751948, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_266", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KRANJI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>20E6B767B2F69FB8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.761754685561, 1.42512594915704, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_267", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT PANJANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>09D276CE9D28C5F6</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.763063555247, 1.37802038374966, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_268", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT PANJANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E6C3D8E13B430D8D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.763178520546, 1.3778632485676, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_269", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SAMUDERA LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5773D3B5DB9A561F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.901911509627, 1.41586536048285, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_270", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SAMUDERA LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4D74C24533C50685</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.902361560111, 1.41590585709171, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_271", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TECK LEE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3025BE9395030FE0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.906781529506, 1.4128757347357, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_272", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TECK LEE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B648DCD0478F77B3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.906510789934, 1.41292900309077, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_273", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SAM KEE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D716A4040285BEB9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.905038571005, 1.40970250004176, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_274", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PETIR LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B0600A1212F651AE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.766793014963, 1.37760322809516, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_275", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PENDING LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>17E89960BD47612A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.771327842297, 1.37629711719364, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_276", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BANGKIT LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B7DF61CC7C6C3B13</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.772590574209, 1.37985334676455, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_277", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FAJAR LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A324402CF624723A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.770675518445, 1.38446507117965, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_278", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SEGAR LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>54BFE6EB924CBA6F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.769409914802, 1.38781155599523, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_279", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>JELAPANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>45CDB0D9BDC73079</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.764676792845, 1.38683269953093, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_280", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2E179BE5B3B6B091</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.942653681878, 1.35419392760642, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_281", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GEYLANG BAHRU MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>31AEB6AC99986A23</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.871555607569, 1.32156169874025, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_282", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES EAST MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CBB2A963BF33C276</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.954540440781, 1.35646076166742, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_283", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHOA CHU KANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>51E0066A5AD1C4F1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.744557135368, 1.38543128087598, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_284", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>9C4A361AF93F75D2</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832770796508, 1.30416113765593, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_285", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 7</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4E4D642287D1546E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832171601239, 1.30316753447888, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_286", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SOMERSET MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>87BD98DB1E154EDA</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.838458985164, 1.30076651877174, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_287", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DHOBY GHAUT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>348F2D3D9B9B7760</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846787971786, 1.29844509204817, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_288", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DHOBY GHAUT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>BBEA402CB9EF9C2E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.845229558929, 1.29905589070891, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_289", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DHOBY GHAUT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F3513EFAD04F7A68</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.84537229423, 1.29974847066982, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_290", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DHOBY GHAUT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3DC596781F39163C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.845842318018, 1.29963489191106, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_291", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUGIS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>9A6B839F19EEBCD4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.855685703914, 1.30000694069987, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_292", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CITY HALL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>762708380AC6662A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.853020413551, 1.29322711034183, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_293", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CITY HALL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>16ABA07E07EB8482</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851960217746, 1.29309991857995, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_294", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUGIS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5BA6737A900E594D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.856336627673, 1.3007821987657, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_295", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUGIS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A06B8C4EF9D0D3CF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.855924081461, 1.30120408956464, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_296", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>OUTRAM PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>9240996FD39C6AB0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.838657701712, 1.2816024282435, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_297", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>OUTRAM PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E6EE8657F042CAA7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.838934837217, 1.28167534568857, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_298", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TANJONG PAGAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B6A65715434862A9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846544590474, 1.2766717422527, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_299", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TANJONG PAGAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1052749762F1E1E3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.84483401421, 1.27640850073994, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_300", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TANJONG PAGAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>44F1B6273F87FEDF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846533557964, 1.27608922999502, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_301", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TANJONG PAGAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8B6F9D10513EFA49</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846908800204, 1.27606482826343, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_302", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RAFFLES PLACE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit H</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3363214AF138AD7A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851621715866, 1.28525828120125, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_303", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RAFFLES PLACE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>40F9660EB1C4C12E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851522595155, 1.28321158212436, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_304", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RAFFLES PLACE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F5DECEDB9FFE5F33</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851479978299, 1.28460687453702, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_305", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RAFFLES PLACE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit G</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>229B46DF8A1250CC</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851478131997, 1.28501689730178, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_306", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RAFFLES PLACE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7908CDF860503E38</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851438353658, 1.28412514770369, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_307", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RAFFLES PLACE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>BDC3B51F0318BD2C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851353214562, 1.28364139087742, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_308", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DHOBY GHAUT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>94631C7F11FCE747</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.845030131864, 1.29993066561743, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_309", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>OUTRAM PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7A26F4DA0D6151ED</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.840386787096, 1.28021335386912, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_310", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>OUTRAM PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C6452A7F824358BC</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.839549241441, 1.2789469557329, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_311", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>OUTRAM PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 6</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CBF6275E32E848D8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.838537251968, 1.27905123851202, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_312", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CLARKE QUAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4217F1A02B8B47A7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846665833855, 1.28938385187247, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_313", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CLARKE QUAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>18FD71C7FA6C0E2A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.847129742733, 1.28940556680266, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_314", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CLARKE QUAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>BC3B9F37FEEA2F7D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846064543773, 1.28689878783571, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_315", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CLARKE QUAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E4C1DE41B6ED8311</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.847032643299, 1.28900018307139, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_316", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHINATOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>872CCBBC52D205A8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.842812853584, 1.2846748973681, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_317", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHOA CHU KANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C3478FBF9D9DA5A3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.744595143026, 1.38497966418718, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_318", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHINATOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A5191DE73F930CFE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.844428119405, 1.28504408060892, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_319", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>QUEENSTOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2B9707C0F0ECEFC8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.806182777133, 1.29424726215095, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_320", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>REDHILL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>08AE2164E30DF4A7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.816994458553, 1.28937070467351, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_321", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>REDHILL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C665A340A5A6CBEC</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.81724937308, 1.28948587536099, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_322", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>COMMONWEALTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>07C70283FD8BEFD4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.798057882031, 1.30275277699775, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_323", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>COMMONWEALTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2DDDE6ED2CFECBB8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.797958292266, 1.30264816140773, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_324", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TIONG BAHRU MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B5B30BE2B81082CE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.826990595146, 1.28575945589882, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_325", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TIONG BAHRU MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6D56F8B0A81292DB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.827496413393, 1.28636194139043, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_326", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HARBOURFRONT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B43EC60E1D8817B8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.822531651626, 1.26525988373469, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_327", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HARBOURFRONT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F2A67B74844A3E35</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.820508811132, 1.2651546823432, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_328", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7055832BEE42BDA7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832039800882, 1.30456047411925, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_329", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>EA3B90BA295EFD84</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832524259379, 1.30489217813715, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_330", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>NEWTON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3A315A2361D88723</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.837881325391, 1.31259779668808, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_331", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>NEWTON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B75D0390F4B7FDF5</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.838219069361, 1.31207546212127, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_332", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ORCHARD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5EA01AD2A9EE377D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832619611515, 1.30435439856042, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_333", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LAVENDER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>67781AFE7035C87A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.863043062903, 1.30716946047274, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_334", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LAVENDER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>79E658884200F5CF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.862650316623, 1.30761853913167, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_335", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KALLANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6CF9B446E7CA4054</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.871587075861, 1.3117817705438, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_336", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KALLANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C4FA9AF262FF43C1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.871730564168, 1.31154048297553, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_337", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ALJUNIED MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7C42B43D848F246C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.882472887782, 1.31648793030408, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_338", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ALJUNIED MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>045EB7203A0A5B4F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.31623848534471, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_339", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LITTLE INDIA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6ABD7125ED6CAD9B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.30656355535778, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_340", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARRER PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4A1DAEFD59468463</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.3135439659757, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_341", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARRER PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6F1834F3D5171B51</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.85265097068, 1.31188353723816, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_342", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARRER PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F1BDA4510E572B33</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.853199391142, 1.31142204648133, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_343", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PROMENADE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>9EAAD1597236079B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.860673611302, 1.29171166099886, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_344", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TOA PAYOH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4345E66C22E984AC</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.847723804314, 1.33237644203021, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_345", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LAKESIDE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>33F25E28EB4DD76D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.720448529393, 1.3439833412622, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_346", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHINESE GARDEN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>34270BC7D85E5169</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.733393135763, 1.34156582633258, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_347", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHINESE GARDEN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F57ABC73B061CF8C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.732741825545, 1.34198208956493, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_348", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHINESE GARDEN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D5745B5A724E8909</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.732220154829, 1.34243874323998, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_349", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>JURONG EAST MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>62C94126CEE417F0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.742762081653, 1.33274868462584, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_350", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>JURONG EAST MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C166FA1DA983F4C3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.741759898309, 1.33398960393819, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_351", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>JURONG EAST MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>133799CC6DC30D46</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.742640162983, 1.33252623564817, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_352", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YEW TEE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>35EAF4829E8E58D0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.747329880035, 1.39806776806003, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_353", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YEW TEE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3C305BEEF18066B9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.747195166471, 1.39765547935269, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_354", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YEW TEE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A5C5202910FC0B3E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.74743189325, 1.39700077173066, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_355", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YEW TEE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>992342EAF6984144</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.747111431, 1.39727251197228, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_356", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT GOMBAK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DE40965F683CCE3E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.752092995698, 1.35869526126909, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_357", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KEAT HONG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C7644F242A74A168</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.749226841606, 1.37846496701683, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_358", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TECK WHYE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E0F222D52301D3A3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.753826102342, 1.37656419493699, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_359", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PHOENIX LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C7F6410B1E2A01E2</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.758252475106, 1.37861406821397, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_360", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT PANJANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>68EDD0201493E175</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.759597843573, 1.38074511730578, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_361", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUANGKOK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7C714ED058155F67</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230726173129</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.892883836731, 1.3834097909899, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_362", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TELOK AYER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0482FD5CBD7B912B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.84877332816, 1.28249727503873, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_363", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ONE-NORTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5C641090FA13AD51</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.787784810554, 1.30007470288421, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_364", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KENT RIDGE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AE7207C0705527D0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.784490451682, 1.29398478516701, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_365", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KENT RIDGE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E895460A627947D4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.29321880284117, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_366", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LITTLE INDIA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>ADDF2D4CC49805FA</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.30685473719087, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_367", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT PANJANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>46B17181D8944278</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.37876623763886, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_368", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHOA CHU KANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AADCDA84B1CB7302</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.744560000291, 1.38539999996952, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_369", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHOA CHU KANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4DDA5EEBC0D64FFE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.744470000052, 1.38459999970902, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_370", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHOA CHU KANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>BFC5700660BC9DF6</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.74447000019, 1.38572999956458, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_371", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHOA CHU KANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>10C24E69D8E231DD</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.744110000392, 1.38536999970775, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_372", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHOA CHU KANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B31E5041452C3958</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.744620000447, 1.38499999972745, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_373", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GUL CIRCLE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F7C62DDD2A73BCC1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.660444143859, 1.31903226656851, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_374", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>GUL CIRCLE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E5B02C31AEAEB829</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.659434945517, 1.31883718313345, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_375", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TUAS CRESCENT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6395E3FC8B63D35B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.648958741267, 1.32076381711222, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_376", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TUAS CRESCENT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D98379EE652ED92E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.649286789109, 1.32124359639868, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_377", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TUAS WEST ROAD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7C93D36D12D462DA</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.639284344709, 1.32987242413926, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_378", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TUAS WEST ROAD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>9982881E8A76BEDD</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.639790711295, 1.33018233023207, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_379", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TUAS LINK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>124B3EF02A2AD8EF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.637245720022, 1.34042689920514, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_380", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TUAS LINK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4EB65BBF499F04BE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.636840667474, 1.34094007226858, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_381", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>EXPO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E79BB6D2D2002821</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.962161021864, 1.33595097171569, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_382", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>EXPO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7559EFD8098C1D02</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.963100681762, 1.33483488834327, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_383", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MACPHERSON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5285256719923C73</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.888615211677, 1.32619277755048, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_384", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>EXPO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>906C9579A92CF7A4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.962123376874, 1.33486331350548, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_385", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MACPHERSON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DB18BEBDD5326092</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.888454834376, 1.32514545867968, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_386", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>EXPO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit G</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0C14825AD577B2E3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.963049007432, 1.33501932038682, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_387", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>EXPO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8F171DDEC2A3C476</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.961836795084, 1.33548282216596, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_388", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>EXPO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6E43F01A7FD410B2</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.96286574628, 1.33471194824107, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_389", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SPRINGLEAF MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CAB3151915361262</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.818390607138, 1.39782080405751, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_390", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SPRINGLEAF MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8D6ED46D4B845735</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.818081623801, 1.39768417887856, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_391", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS NORTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>469EE4BF80F069CC</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.785429080675, 1.44793650249651, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_392", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS NORTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>72D10D55C51FC3F5</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.785102271425, 1.44904729970725, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_393", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>372C516598D98235</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.788964328113, 1.43584393593589, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_394", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 7</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AD7053030CB8BF9F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.787976768822, 1.43449552583778, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_395", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS SOUTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>45D78AD571DA0977</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.793027463346, 1.4273020172478, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_396", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS SOUTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E9A31A80A570C976</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.793413692732, 1.42760496413455, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_397", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS SOUTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6518624D5602BB64</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.794560497858, 1.42742676003562, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_398", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS SOUTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2F1F88360B17E436</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.794423831787, 1.42668424080276, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_399", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SPRINGLEAF MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>122980157DCB57C6</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.818003414954, 1.39878724836535, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_400", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LENTOR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1D02FE80AE160F4F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.835823452024, 1.38589099008684, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_401", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LENTOR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AD8355986951011E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.835359976701, 1.38540982990749, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_402", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LENTOR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>26CD4A0F04FEAE7A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.837398080029, 1.38460195423691, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_403", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LENTOR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>872548D8089E5C63</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.837338660064, 1.3836277515433, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_404", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LENTOR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1DBB7D760352881C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.836839532696, 1.38416237536177, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_405", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MAYFLOWER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>03D1FF1990E162EB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.837461041245, 1.37353209209743, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_406", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MAYFLOWER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4F085EF4AAB00599</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.837031676998, 1.37371976104919, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_407", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MAYFLOWER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4C504570F6BC8EC3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.83671675131, 1.37270397234703, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_408", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MAYFLOWER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 7</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0C15214EBA575804</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.83628496724, 1.37137730761848, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_409", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MAYFLOWER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AE51C872434B37D7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.836795978097, 1.37127632290154, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_410", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MAYFLOWER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>728784A5D5FEAA7A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.836760326587, 1.37077733815485, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_411", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MAYFLOWER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 6</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>64DA1E7B1619F148</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.836308734746, 1.37089614419322, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_412", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRIGHT HILL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CFB9350F44E8991A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833635413994, 1.36403718216562, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_413", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRIGHT HILL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F273E9D550B06062</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833510631569, 1.36328276235517, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_414", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRIGHT HILL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>BC4A442B12F99E85</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.831953829452, 1.3631639565255, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_415", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BRIGHT HILL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2A45076ED986B275</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832375711055, 1.36218380484981, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_416", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UPPER THOMSON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3EA737829473A972</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.831833009004, 1.35573657470655, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_417", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UPPER THOMSON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>02A569EE1B9EC73B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.831916196892, 1.35483364468235, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_418", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UPPER THOMSON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2313E9492AA32861</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.83263517825, 1.35441782186373, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_419", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UPPER THOMSON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A00E2C99CAD73E80</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833924590606, 1.35414456637885, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_420", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UPPER THOMSON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7A835D396BF0703F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833793867314, 1.35350301029652, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_421", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CALDECOTT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>00BCCDD48A0A19AB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.840387499238, 1.33825972994181, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_422", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CALDECOTT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>857DEE03468202D9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.840464745299, 1.33783202317926, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_423", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CALDECOTT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>907574C903E3C5B1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.839882429752, 1.33675087511065, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_424", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CANBERRA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DD34E7FB97694745</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.829254643947, 1.44351133884481, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_425", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CANBERRA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>749D0975B17D266E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.829527975861, 1.44363014106056, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_426", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CANBERRA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B2A3B66D0D93D04A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.829700293337, 1.44280446541205, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_427", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CANBERRA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5CC317629EEEF874</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.829961741209, 1.44293514818295, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_428", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CANBERRA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>9BEA4511424691E9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.830116233173, 1.44325591355701, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_429", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 2</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A981B455F405BCC2</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.786508083762, 1.43667311468753, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_430", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 3</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>ABE9AA34AD7D1F14</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.787108225411, 1.43724336703986, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_431", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 4</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D82504FE62821345</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.788005465669, 1.43754037357975, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_432", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 1</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5CB3F601FBDF6CC4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.785735624465, 1.43669687524624, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_433", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 6</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F7F55451430108C4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.789089109752, 1.43481517513889, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_434", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>WOODLANDS SOUTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit 5</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DADA67E3382ACDE0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.79436521102, 1.42584338690928, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_435", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FERNVALE LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>92D152D45E445F84</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.876261653892, 1.3921582676261, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_436", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT PANJANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C1FCA2C604034AB0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.762722523556, 1.37792792775255, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_437", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT PANJANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0D013B4A269EF17B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.763251851956, 1.37802410264834, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_438", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TANAH MERAH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A923EE234A94C8FF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.945723041582, 1.32706127581436, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_439", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TANAH MERAH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>36F95FB516EC35CE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.945794727929, 1.32685774838818, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_440", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CLEMENTI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A9519BF912A2133C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.764982102595, 1.31537355787444, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_441", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CLEMENTI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B7D91BDACAA183E1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.765185566729, 1.31546912943168, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_442", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUONA VISTA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>EE2D2107B28FC4C5</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.790449175303, 1.30730945077024, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_443", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUONA VISTA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>95621469AB2856E4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.790656619433, 1.30719725705854, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_444", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUONA VISTA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F92A8D113B600232</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.79059939037, 1.30691288861046, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_445", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>QUEENSTOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C54B2E9241AC7587</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.806311472758, 1.29432990890605, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_446", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHENG LIM LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FC07F81DEF01BAAF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.893684874277, 1.39637321823541, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_447", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHENG LIM LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1BE9F4E72D049751</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.893920331573, 1.3961869711233, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_448", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>THANGGAM LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>16EC7E9FB469AE26</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.875566364382, 1.39719803592397, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_449", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>THANGGAM LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>256206889802450A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.875686836958, 1.39719245833865, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_450", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KUPANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>62EDCA2810CF78CE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.881139169799, 1.39828419776009, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_451", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KUPANG LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>EC95D6994C36C7C2</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.881137936207, 1.39815879147241, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_452", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARMWAY LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B5FE598CC9473CDE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.889407447882, 1.39720034457508, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_453", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARMWAY LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C66585DE1453A1EF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.889115016848, 1.39713347750584, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_454", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LAYAR LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>213B4B13C275292B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.880159730519, 1.39202617213173, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_455", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LAYAR LRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F757F13626617675</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.880142799143, 1.39214453741467, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_456", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KRANJI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E0BD26E3F5EE18B9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.761940606096, 1.42485264466106, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_457", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KRANJI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>91BDDFEBA5A15383</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.762018515591, 1.42473917121754, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_458", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARSILING MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>BC10B89687EB57DF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.773820451036, 1.43246952240006, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_459", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARSILING MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A4DF45D1ABCFAC02</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.774210493312, 1.43280169435331, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_460", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PAYA LEBAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CC4D13D27CAEA2B1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.892788324105, 1.31821599215008, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_461", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PAYA LEBAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>69CB616E4522ABA4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.892217168632, 1.31834746689885, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_462", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YIO CHU KANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>896C97CCBA89A32A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.844645760434, 1.38195362130605, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_463", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YIO CHU KANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>03DE662B8A72F32A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.844834195491, 1.3816444074307, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_464", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YIO CHU KANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F73C9CE1485EDFF0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.844946847542, 1.38154752053604, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_465", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ADMIRALTY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>887FE97DECFFEA05</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.80054245816, 1.44059628565794, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_466", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ADMIRALTY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>071776EA2CE1E3E9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.800989521918, 1.44034566128304, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_467", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SEMBAWANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A4391FC0AAA1EAAC</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.820109735758, 1.44883731943313, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_468", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SEMBAWANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>3167E33222F7BAC9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.819790204572, 1.44876826032584, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_469", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YISHUN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4D6714260A96395A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.834783603184, 1.42939716731595, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_470", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>YISHUN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7CCB5AAD7A692ED6</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.834863485217, 1.42910650744289, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_471", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KHATIB MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>BEC9777C2AF5693A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832886913694, 1.41754470685258, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_472", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KHATIB MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0EB1742925CA5FD4</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.83292241701, 1.41705821242698, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_473", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KHATIB MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DFC9A990CD03CDD1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.832706667743, 1.41714341792972, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_474", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SEMBAWANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A612A01DC4DD39AA</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.820401560292, 1.44892733153759, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_475", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PASIR RIS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5F3BC6B21D0848C7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.949136940349, 1.37288623635201, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_476", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PASIR RIS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A801E2A1E34520C9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.949333426645, 1.37284196908053, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_477", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6D821717AFC18096</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.944766312107, 1.35376401254022, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_478", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>ACC2B4758E58EF1B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.944903957895, 1.35339831264921, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_479", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHOA CHU KANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2E2B70E0E2D25273</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.38573618540312, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_480", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LITTLE INDIA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>8A50DC395B46AA31</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.30635155408624, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_481", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LITTLE INDIA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6710D2143B21969B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.30742644439895, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_482", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARRER PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>173209DA7F23FABE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.31245360639306, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_483", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARRER PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6551D0EB84199FBF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.853266824822, 1.31191067869135, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_484", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARRER PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>360853AB3FB5F8E8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.854772747378, 1.31232690903703, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_485", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FARRER PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit G</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E130D0857A648E60</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.855500982372, 1.31225903641299, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_486", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHANGI AIRPORT MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>18E22F13F5B553EB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.987304579798, 1.35722025606017, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_487", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ESPLANADE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit G</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2ACFF8F7FE329CCA</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.85492020244, 1.29358640073634, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_488", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ESPLANADE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>37A435E4166C73B7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.855855203772, 1.29290433691952, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_489", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ESPLANADE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>651EA817751D16D3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.856175670935, 1.29292718733646, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_490", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RAFFLES PLACE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D79CE3109CFB1331</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851562535487, 1.28363052072741, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_491", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RAFFLES PLACE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit J</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>244D66DE744BCD47</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851296033431, 1.28262623356678, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_492", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RAFFLES PLACE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0172739C99613BF7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.850960902093, 1.28253857491919, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_493", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>RAFFLES PLACE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit I</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5783A6CCBAC00283</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.851433302399, 1.28238230729746, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_494", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TANJONG PAGAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>BD38C5FD98D816FF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.847095791197, 1.27625910113258, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_495", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BENCOOLEN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0DF43E9348A676BC</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.849560477591, 1.29819940045567, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_496", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES EAST MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FFF4C9A7B530851C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.955704711379, 1.35555317150178, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_497", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MATTAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6B8EB7C225C36578</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.882732067245, 1.3272514140918, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_498", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CFE16BC8ECCE4D1A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.942658810694, 1.35585344476425, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_499", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UPPER CHANGI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>23F915FD4BA908AB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.961328480539, 1.34186119734213, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_500", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES EAST MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>192449D843551047</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.955170014221, 1.35558822639472, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_501", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>JALAN BESAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F845CB06D67E6A83</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.855244564615, 1.30546727989327, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_502", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEDOK NORTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D120C83FD7AA7B80</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.918049823987, 1.33512626621364, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_503", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UPPER CHANGI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FC43DB54B0E61973</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.96096063019, 1.3420065422681, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_504", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEDOK NORTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>472D306FE205DA62</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.919062823449, 1.33416607444721, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_505", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BENDEMEER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5EC1075AF445471C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.863219231431, 1.3134807250256, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_506", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES WEST MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6F2A56A935D2673D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.938196740006, 1.34563058761391, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_507", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UBI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7CB7D918F6B5E915</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.898585570916, 1.32983077067473, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_508", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FORT CANNING MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AF446178B7F1A586</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.844425031565, 1.29283027594572, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_509", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEDOK NORTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4D84939055694394</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.919134302662, 1.33437249020327, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_510", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MATTAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>63BE655DB172CD4E</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.883748387313, 1.32646352216707, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_511", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>FORT CANNING MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FC551044CAEAB201</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.844170034197, 1.29240609557825, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_512", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UBI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CAE96AD9A152877B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.899876690857, 1.32995082828977, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_513", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UPPER CHANGI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>9EAAA93AC64D4C2B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.96161727308, 1.34199266734225, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_514", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>UPPER CHANGI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D12B752513687695</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.961476268426, 1.34103767912256, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_515", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAMPINES EAST MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>99EACB07EB87275F</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.955717437922, 1.35697019355667, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_516", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TANJONG PAGAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>39E16699C1976560</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846927580235, 1.27737948810714, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_517", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TANJONG PAGAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit G</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E1CC299B43F4C1C1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846953147544, 1.27778454036503, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_518", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TANJONG PAGAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit H</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>E922D2F204D2DA84</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.846445088412, 1.27545090434532, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_519", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ESPLANADE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit E</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5A8D40F4CDB7B0AD</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.855567841069, 1.29312881366023, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_520", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ESPLANADE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C6AC4E01D3816D01</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.855104632111, 1.29381950731267, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_521", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ESPLANADE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>63956E6E46ECE3E0</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.85583375579, 1.29262374346665, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_522", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PROMENADE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>BCC2873D65F6A92C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.860136884402, 1.29386208787626, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_523", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>NICOLL HIGHWAY MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CE8447F8465B2FF7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.863505955243, 1.30016968970884, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_524", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>STADIUM MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>402BA10E05281F64</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.876226233975, 1.30337022602025, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_525", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>STADIUM MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>CA5A16272BE38BEE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.874399988289, 1.3025110877234, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_526", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MOUNTBATTEN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>187A879D30D2F951</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.883248292704, 1.30604592566075, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_527", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MOUNTBATTEN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D4AA144302569724</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.882597535734, 1.30675271022847, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_528", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DAKOTA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A66FCFFDD49C15D3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.889307215344, 1.30881418752426, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_529", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DAKOTA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AB5D405E46C647FB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.887979337682, 1.30809675040545, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_530", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PAYA LEBAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>0EA7336E0E7F32CF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.891721121592, 1.31814928543131, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_531", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PAYA LEBAR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>786C0A6632548330</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.892077699246, 1.31658771946928, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_532", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>PROMENADE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DF287781847E65C1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.860236484112, 1.29410275636151, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_533", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KHATIB MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>67A576351EE1E7DB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.833231020039, 1.41786903711686, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_534", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SIMEI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>FE7E73B9E2A00E1D</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.953818676297, 1.34266179453371, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_535", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEDOK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D5D6C1E315FDB9D7</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.929404279689, 1.32407464586546, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_536", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUGIS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1AC0E726E51FF630</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.856890654867, 1.29907884888673, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_537", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CITY HALL MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6F31D6D44E1CD68C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.852980287708, 1.29304184790826, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_538", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>QUEENSTOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>45F7FB42BEF80E73</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.805584729896, 1.2954462560701, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_539", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>QUEENSTOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>323A1F8C0515C318</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.805492204888, 1.29538293052613, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_540", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>COMMONWEALTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4759AD8699CAAFEB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.798778572605, 1.30207704580319, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_541", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>COMMONWEALTH MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5C10631FF88A2A31</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.798720258772, 1.30201271935133, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_542", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CLEMENTI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1BCD78C2E2F0AEBA</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.765570116184, 1.314078414345, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_543", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CLEMENTI MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>B42D779AB82F42CF</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.765749815719, 1.31416229810691, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_544", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>JURONG EAST MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F6949D2149A78ED9</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.742298193307, 1.33358133108919, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_545", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ANG MO KIO MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>2FA1606517F7F394</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.84982457248, 1.36988237526817, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_546", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SOMERSET MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>947DEBE26C775910</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.838765584281, 1.30043622058993, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_547", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SOMERSET MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit D</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C8C03BAA2804D6D1</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.839734148085, 1.30024113867986, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_548", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARINA SOUTH PIER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F068727788589ACD</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.86233005097, 1.27052998951863, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_549", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>MARINA SOUTH PIER MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>C73EE0EE2FBED722</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.863579669764, 1.27143783716222, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_550", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHINATOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit G</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>6EAFAD60E5DC5EB3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.84445854173, 1.28473608363703, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_551", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>CHINATOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>F311BD73BF790D1C</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.845462531796, 1.28427477607278, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_552", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BUKIT PANJANG MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>BE5F7B68E04895BE</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.761370504957, 1.37861889401251, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_553", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>HILLVIEW MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>4A8EA2D66E8ECDA8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.767688290103, 1.36247423794507, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_554", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEAUTY WORLD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D320410A69021932</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.775984296226, 1.3416961499328, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_555", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BEAUTY WORLD MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>7E2D6AAB2D36BBBA</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.775761545626, 1.33998187378458, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_556", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>KING ALBERT PARK MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>DAE9E8760965D45A</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.784561584781, 1.33597604551556, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_557", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>SIXTH AVENUE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>10CC04828E7C82D8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.796848178229, 1.33152130143572, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_558", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>TAN KAH KEE MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>A5B4AAE12B40643B</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.807800873707, 1.32614615882549, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_559", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>BOTANIC GARDENS MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>1D8ACE3D804EFC33</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.815914145792, 1.32268461233639, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_560", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>NEWTON MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit C</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>D977970135729EB8</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.31361726914981, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_561", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>LITTLE INDIA MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit F</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5617CFEE626840BB</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.30768353655885, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_562", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>ROCHOR MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit B</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>5F1E6F573314E1E3</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.************, 1.30421477703374, 0.0]}}, {"type": "Feature", "properties": {"Name": "kml_563", "Description": "<center><table><tr><th colspan='2' align='center'><em>Attributes</em></th></tr><tr bgcolor=\"#E3E3F3\"> <th>STATION_NA</th> <td>DOWNTOWN MRT STATION</td> </tr><tr bgcolor=\"\"> <th>EXIT_CODE</th> <td>Exit A</td> </tr><tr bgcolor=\"#E3E3F3\"> <th>INC_CRC</th> <td>AD2F8B7AE573BE63</td> </tr><tr bgcolor=\"\"> <th>FMEL_UPD_D</th> <td>20230127204435</td> </tr></table></center>"}, "geometry": {"type": "Point", "coordinates": [103.852646560011, 1.27977345528297, 0.0]}}]}